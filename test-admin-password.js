#!/usr/bin/env node

/**
 * 🔍 Test Admin Password
 * 
 * Tests different passwords for admin user
 */

import 'dotenv/config';
import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';

async function testAdminPassword() {
  try {
    const url = process.env.AIVEN_MYSQL_URL || process.env.DATABASE_URL;
    const connection = await mysql.createConnection(url);
    
    // Get admin user
    const [adminUsers] = await connection.execute(
      "SELECT id, username, email, password_hash, role FROM users WHERE email = ?",
      ['<EMAIL>']
    );
    
    if (adminUsers.length > 0) {
      const admin = adminUsers[0];
      console.log('Admin user found:', admin.email);
      
      // Test different passwords
      const passwords = ['password', 'admin123', 'admin', '123456', 'password123'];
      
      for (const pwd of passwords) {
        const isValid = await bcrypt.compare(pwd, admin.password_hash);
        console.log(`Password "${pwd}": ${isValid ? '✅ VALID' : '❌ INVALID'}`);
        
        if (isValid) {
          console.log(`\n🎉 Correct password found: ${pwd}`);
          break;
        }
      }
      
      // Also show the hash for reference
      console.log(`\nPassword hash: ${admin.password_hash}`);
      
    } else {
      console.log('No admin user found');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testAdminPassword();
