import fetch from 'node-fetch';

// Test 1: Basic Connection Test (as requested)
const testConnection = async () => {
  console.log('Testing Aiven MySQL connection...');
  try {
    const response = await fetch('http://localhost:5000/api/database/test-connections');
    const result = await response.json();
    console.log('Test Result:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success && result.data.results['aiven-mysql']) {
      console.log('\n✅ Aiven MySQL connection successful!');
      return true;
    } else {
      console.error('\n❌ Aiven MySQL connection failed.');
      if (result.error) {
        console.error('Error details:', result.error);
      }
      return false;
    }
  } catch (error) {
    console.error('An error occurred while testing the connection:', error);
    return false;
  }
};

// Test 2: Database Status
const testDatabaseStatus = async () => {
  console.log('\n' + '='.repeat(50));
  console.log('Testing Database Status...');
  try {
    const response = await fetch('http://localhost:5000/api/database/status');
    const result = await response.json();
    
    console.log('Database Status:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success && result.data.currentProvider === 'aiven-mysql') {
      console.log('\n✅ AIVEN MySQL is the current active provider!');
      
      // Show health information
      if (result.data.health) {
        const health = result.data.health;
        console.log(`🏥 Health Status: ${health.overall}`);
        console.log(`⏱️  Uptime: ${(health.uptime / 1000).toFixed(1)} seconds`);
        
        if (health.checks && health.checks.length > 0) {
          const aivenCheck = health.checks.find(check => check.provider === 'aiven-mysql');
          if (aivenCheck) {
            console.log(`⚡ Response Time: ${aivenCheck.responseTime}ms`);
            console.log(`📊 Status: ${aivenCheck.status}`);
          }
        }
      }
      
      return true;
    } else {
      console.error('\n❌ AIVEN MySQL is not the current provider');
      return false;
    }
  } catch (error) {
    console.error('Error testing database status:', error);
    return false;
  }
};

// Test 3: Provider Switch Test
const testProviderSwitch = async () => {
  console.log('\n' + '='.repeat(50));
  console.log('Testing Provider Switch to AIVEN MySQL...');
  try {
    const response = await fetch('http://localhost:5000/api/database/switch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        provider: 'aiven-mysql'
      })
    });
    
    const result = await response.json();
    console.log('Switch Result:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Successfully switched to AIVEN MySQL!');
      return true;
    } else {
      console.log('\n⚠️  Switch result:', result.message || result.error);
      return false;
    }
  } catch (error) {
    console.error('Error testing provider switch:', error);
    return false;
  }
};

// Test 4: Health Check
const testHealthCheck = async () => {
  console.log('\n' + '='.repeat(50));
  console.log('Testing Database Health...');
  try {
    const response = await fetch('http://localhost:5000/api/database/health');
    const result = await response.json();
    
    console.log('Health Check Result:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Database health check successful!');
      return true;
    } else {
      console.error('\n❌ Database health check failed');
      return false;
    }
  } catch (error) {
    console.error('Error testing database health:', error);
    return false;
  }
};

// Test 5: Performance Test
const testPerformance = async () => {
  console.log('\n' + '='.repeat(50));
  console.log('Testing AIVEN MySQL Performance...');
  
  const iterations = 5;
  const times = [];
  
  try {
    for (let i = 0; i < iterations; i++) {
      const start = Date.now();
      const response = await fetch('http://localhost:5000/api/database/status');
      await response.json();
      const time = Date.now() - start;
      times.push(time);
      console.log(`Request ${i + 1}: ${time}ms`);
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log('\n📊 Performance Results:');
    console.log(`Average: ${avgTime.toFixed(1)}ms`);
    console.log(`Minimum: ${minTime}ms`);
    console.log(`Maximum: ${maxTime}ms`);
    
    if (avgTime < 500) {
      console.log('✅ Performance: Excellent (< 500ms)');
    } else if (avgTime < 1000) {
      console.log('⚠️  Performance: Good (< 1000ms)');
    } else {
      console.log('❌ Performance: Needs improvement (> 1000ms)');
    }
    
    return true;
  } catch (error) {
    console.error('Error testing performance:', error);
    return false;
  }
};

// Main test runner
const runAllTests = async () => {
  console.log('🚀 AIVEN MySQL Comprehensive Test Suite');
  console.log('=' .repeat(60));
  
  const results = {
    connection: false,
    status: false,
    switch: false,
    health: false,
    performance: false
  };
  
  // Run all tests
  results.connection = await testConnection();
  results.status = await testDatabaseStatus();
  results.switch = await testProviderSwitch();
  results.health = await testHealthCheck();
  results.performance = await testPerformance();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  
  const testNames = {
    connection: 'Connection Test',
    status: 'Database Status',
    switch: 'Provider Switch',
    health: 'Health Check',
    performance: 'Performance Test'
  };
  
  let passed = 0;
  let total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${testNames[test]}: ${status}`);
    if (result) passed++;
  });
  
  console.log('\n' + '='.repeat(60));
  console.log(`📈 Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All AIVEN MySQL tests PASSED! Database is fully functional.');
  } else {
    console.log('⚠️  Some tests failed. Please check the results above.');
  }
  
  console.log('='.repeat(60));
};

// Run the comprehensive test suite
runAllTests();
