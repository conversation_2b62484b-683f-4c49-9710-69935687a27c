import mysql from 'mysql2/promise';
import { drizzle as drizzleMysql } from 'drizzle-orm/mysql2';
import { drizzle as drizzlePostgres } from 'drizzle-orm/postgres-js';
import { drizzle as drizzleSqlite } from 'drizzle-orm/better-sqlite3';
import postgres from 'postgres';
import Database from 'better-sqlite3';
import * as schema from '@shared/schema';
import { DatabaseConfig, DatabaseProvider } from '../config/database';
import { AivenMySQLDatabase } from './aiven-mysql';
import { pool as mysqlPool, db as mysqlDb } from '../db';
import { sqliteDb } from '../sqlite-db';

export interface DatabaseConnection {
  db: any;
  provider: string;
  testConnection(): Promise<boolean>;
  close(): Promise<void>;
}

export class MySQLDatabase implements DatabaseConnection {
  public db: any;
  public provider = 'mysql';
  private pool: mysql.Pool;

  constructor(config: DatabaseConfig) {
    this.pool = mysql.createPool({
      uri: config.url,
      ssl: config.ssl ? {
        rejectUnauthorized: true,
        ...(config.caCert && { ca: config.caCert })
      } : false,
      waitForConnections: true,
      connectionLimit: config.poolConfig?.connectionLimit || 10,
      queueLimit: config.poolConfig?.queueLimit || 0,
      acquireTimeout: config.poolConfig?.acquireTimeout || 60000,
      timeout: config.poolConfig?.timeout || 60000
    });

    this.db = drizzleMysql(this.pool, { schema, mode: 'default' });
  }

  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      console.error('MySQL connection test failed:', error);
      return false;
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
  }
}

export class DatabaseFactory {
  private static instances: Map<DatabaseProvider, any> = new Map();

  static async create(config: DatabaseConfig) {
    const { provider } = config;

    // Return existing instance if available
    if (this.instances.has(provider)) {
      return this.instances.get(provider);
    }

    let instance;

    switch (provider) {
      case 'aiven-mysql':
        instance = new AivenMySQLDatabase(config);
        await instance.testConnection();
        this.instances.set(provider, instance);
        console.log(`✅ ${provider} database initialized`);
        return instance;

      case 'mysql':
        // Use existing MySQL connection
        instance = { db: mysqlDb, pool: mysqlPool };
        this.instances.set(provider, instance);
        console.log(`✅ ${provider} database initialized`);
        return instance;

      case 'sqlite':
        instance = { db: sqliteDb };
        this.instances.set(provider, instance);
        console.log(`✅ ${provider} database initialized`);
        return instance;

      default:
        throw new Error(`Unsupported database provider: ${provider}`);
    }
  }

  static async closeAll(): Promise<void> {
    for (const [provider, instance] of this.instances.entries()) {
      if (instance.close) {
        await instance.close();
      }
      console.log(`🔌 ${provider} connection closed`);
    }
    this.instances.clear();
  }
}