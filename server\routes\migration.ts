/**
 * 🔄 Database Migration API Routes
 * 
 * Handles database migration operations across providers
 */

import { Router } from 'express';
import { MigrationManager } from '../database/migration-manager';
import { SchemaAnalyzer } from '../database/schema-analyzer';
import { ConnectionManager } from '../database/connection-manager';

const router = Router();
const migrationManager = new MigrationManager();
const schemaAnalyzer = new SchemaAnalyzer();
const connectionManager = ConnectionManager.getInstance();

/**
 * GET /api/migration/status
 * Get current migration status and schema information
 */
router.get('/status', async (req, res) => {
  try {
    const currentProvider = connectionManager.getCurrentProvider();
    const schema = await schemaAnalyzer.getCurrentSchema();
    const expectedTables = schemaAnalyzer.getExpectedTables();
    const comparison = await schemaAnalyzer.compareSchemas(expectedTables);
    const migrationHistory = await migrationManager.getMigrationHistory();

    res.json({
      success: true,
      data: {
        currentProvider,
        schemaStatus: {
          totalTables: schema.length,
          expectedTables: expectedTables.length,
          missingTables: comparison.missingTables,
          extraTables: comparison.extraTables,
          isComplete: comparison.missingTables.length === 0
        },
        migrationHistory: migrationHistory.slice(0, 10), // Last 10 migrations
        lastMigration: migrationHistory[0] || null
      }
    });
  } catch (error) {
    console.error('Error getting migration status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get migration status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/migration/schema-report
 * Generate detailed schema report
 */
router.get('/schema-report', async (req, res) => {
  try {
    const report = await schemaAnalyzer.generateSchemaReport();
    const currentSchema = await schemaAnalyzer.getCurrentSchema();

    res.json({
      success: true,
      data: {
        report,
        tables: currentSchema.map(table => ({
          name: table.name,
          columnCount: table.columns.length,
          indexCount: table.indexes.length,
          foreignKeyCount: table.foreignKeys.length,
          columns: table.columns.map(col => ({
            name: col.name,
            type: col.type,
            nullable: col.nullable,
            isPrimaryKey: col.isPrimaryKey
          }))
        }))
      }
    });
  } catch (error) {
    console.error('Error generating schema report:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate schema report',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/migration/apply-schema
 * Apply schema to current database
 */
router.post('/apply-schema', async (req, res) => {
  try {
    const currentProvider = connectionManager.getCurrentProvider();
    
    console.log(`🔄 Applying schema for ${currentProvider}...`);
    
    // Initialize migration tracking
    await migrationManager.initializeMigrationTracking();
    
    // Apply schema
    await migrationManager.applySchema();
    
    // Get updated schema info
    const schema = await schemaAnalyzer.getCurrentSchema();
    
    res.json({
      success: true,
      message: `Schema applied successfully for ${currentProvider}`,
      data: {
        provider: currentProvider,
        tablesCreated: schema.length,
        tables: schema.map(t => t.name)
      }
    });
  } catch (error) {
    console.error('Error applying schema:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to apply schema',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/migration/migrate-data
 * Migrate data between providers
 */
router.post('/migrate-data', async (req, res) => {
  try {
    const { sourceProvider, targetProvider } = req.body;
    
    if (!sourceProvider || !targetProvider) {
      return res.status(400).json({
        success: false,
        error: 'Source and target providers are required'
      });
    }

    if (sourceProvider === targetProvider) {
      return res.status(400).json({
        success: false,
        error: 'Source and target providers cannot be the same'
      });
    }

    console.log(`🔄 Starting data migration from ${sourceProvider} to ${targetProvider}...`);
    
    const result = await migrationManager.migrateData(sourceProvider, targetProvider);
    
    if (result.success) {
      res.json({
        success: true,
        message: `Data migration completed successfully`,
        data: {
          sourceProvider,
          targetProvider,
          tablesProcessed: result.tablesProcessed,
          recordsMigrated: result.recordsMigrated,
          duration: result.duration,
          errors: result.errors
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Data migration failed',
        data: {
          sourceProvider,
          targetProvider,
          tablesProcessed: result.tablesProcessed,
          recordsMigrated: result.recordsMigrated,
          duration: result.duration,
          errors: result.errors
        }
      });
    }
  } catch (error) {
    console.error('Error migrating data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to migrate data',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/migration/providers
 * Get available database providers and their status
 */
router.get('/providers', async (req, res) => {
  try {
    const providers = ['mysql', 'aiven-mysql', 'neon-postgresql', 'sqlite'];
    const currentProvider = connectionManager.getCurrentProvider();
    const connectionResults = await connectionManager.testAllConnections();
    
    const providerStatus = providers.map(provider => ({
      name: provider,
      displayName: provider.replace('-', ' ').toUpperCase(),
      isActive: provider === currentProvider,
      isAvailable: connectionResults[provider] || false,
      description: this.getProviderDescription(provider)
    }));

    res.json({
      success: true,
      data: {
        currentProvider,
        providers: providerStatus,
        availableCount: providerStatus.filter(p => p.isAvailable).length
      }
    });
  } catch (error) {
    console.error('Error getting providers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get providers',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/migration/backup
 * Create backup of current database
 */
router.post('/backup', async (req, res) => {
  try {
    const { name, description } = req.body;
    const currentProvider = connectionManager.getCurrentProvider();
    
    // For now, we'll create a simple backup by exporting schema and data info
    const schema = await schemaAnalyzer.getCurrentSchema();
    const migrationHistory = await migrationManager.getMigrationHistory();
    
    const backup = {
      id: `backup_${Date.now()}`,
      name: name || `Backup ${new Date().toISOString()}`,
      description: description || `Automatic backup of ${currentProvider}`,
      provider: currentProvider,
      createdAt: new Date().toISOString(),
      schema: schema,
      migrationHistory: migrationHistory,
      tableCount: schema.length,
      totalColumns: schema.reduce((sum, table) => sum + table.columns.length, 0)
    };
    
    // In a real implementation, you would save this backup to a file or backup storage
    console.log(`📦 Backup created: ${backup.name}`);
    
    res.json({
      success: true,
      message: 'Backup created successfully',
      data: backup
    });
  } catch (error) {
    console.error('Error creating backup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create backup',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/migration/history
 * Get migration history
 */
router.get('/history', async (req, res) => {
  try {
    const history = await migrationManager.getMigrationHistory();
    
    res.json({
      success: true,
      data: {
        migrations: history,
        totalCount: history.length,
        completedCount: history.filter(m => m.status === 'completed').length,
        failedCount: history.filter(m => m.status === 'failed').length
      }
    });
  } catch (error) {
    console.error('Error getting migration history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get migration history',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Helper function to get provider description
 */
function getProviderDescription(provider: string): string {
  switch (provider) {
    case 'mysql':
      return 'Local MySQL database for development';
    case 'aiven-mysql':
      return 'AIVEN MySQL cloud database with SSL';
    case 'neon-postgresql':
      return 'NEON PostgreSQL serverless database';
    case 'sqlite':
      return 'SQLite local file database (always available)';
    default:
      return 'Database provider';
  }
}

// Add error handling wrapper
function asyncHandler(fn: Function) {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

export default router;
