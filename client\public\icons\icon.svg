<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.2)"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#bg)" />
  
  <!-- Brain icon -->
  <g transform="translate(256,256)" fill="white" filter="url(#shadow)">
    <!-- Left brain hemisphere -->
    <path d="M-60,-80 C-100,-80 -120,-40 -120,0 C-120,40 -100,80 -60,80 C-40,80 -20,60 -20,40 L-20,-40 C-20,-60 -40,-80 -60,-80 Z" />
    
    <!-- Right brain hemisphere -->
    <path d="M60,-80 C100,-80 120,-40 120,0 C120,40 100,80 60,80 C40,80 20,60 20,40 L20,-40 C20,-60 40,-80 60,-80 Z" />
    
    <!-- Brain stem -->
    <rect x="-15" y="60" width="30" height="40" rx="15" />
    
    <!-- Brain details -->
    <path d="M-80,-20 Q-60,-30 -40,-20 Q-20,-10 0,-20 Q20,-10 40,-20 Q60,-30 80,-20" 
          stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none" />
    <path d="M-80,0 Q-60,10 -40,0 Q-20,10 0,0 Q20,10 40,0 Q60,10 80,0" 
          stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none" />
    <path d="M-80,20 Q-60,30 -40,20 Q-20,30 0,20 Q20,30 40,20 Q60,30 80,20" 
          stroke="rgba(255,255,255,0.3)" stroke-width="3" fill="none" />
  </g>
  
  <!-- Islamic crescent -->
  <g transform="translate(380,132)" fill="rgba(255,255,255,0.9)">
    <path d="M0,0 C-20,-20 -20,20 0,0 C15,-10 15,10 0,0 Z" />
    <circle cx="25" cy="-15" r="4" />
  </g>
  
  <!-- Heart symbol (caring) -->
  <g transform="translate(132,380)" fill="rgba(255,255,255,0.8)">
    <path d="M0,8 C-8,0 -16,0 -16,8 C-16,16 0,24 0,24 S16,16 16,8 C16,0 8,0 0,8 Z" />
  </g>
</svg>
