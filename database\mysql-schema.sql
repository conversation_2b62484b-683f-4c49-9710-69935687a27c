-- SantriMental Database Schema - MySQL Version
-- Compatible with MySQL 8.0+
-- Version: 2.0
-- Date: 2024

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS santrimental6;
USE santrimental6;

-- Users table for authentication
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('student', 'counselor', 'admin') DEFAULT 'student',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_users_email (email),
    INDEX idx_users_role (role)
);

-- User profiles with pesantren-specific information
CREATE TABLE profiles (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    nama_lengkap VARCHAR(255) NOT NULL,
    nomor_induk VARCHAR(50),
    jenis_kelamin ENUM('laki-laki', 'perempuan') NULL,
    tanggal_lahir DATE NULL,
    umur INT NULL,
    kelas VARCHAR(100) NULL,
    tingkat_pendidikan ENUM('ibtidaiyah', 'tsanawiyah', 'aliyah', 'mahasiswa', 'lainnya') NULL,
    pondok_pesantren VARCHAR(255) NULL,
    alamat_pesantren TEXT NULL,
    provinsi VARCHAR(100) NULL,
    kota_kabupaten VARCHAR(100) NULL,
    tahun_masuk INT NULL,
    status_santri ENUM('aktif', 'alumni', 'pindah', 'keluar') DEFAULT 'aktif',
    program_studi VARCHAR(255) NULL,
    wali_santri VARCHAR(255) NULL,
    nomor_wali VARCHAR(20) NULL,
    riwayat_konseling BOOLEAN DEFAULT FALSE,
    persetujuan_data BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_profiles_user_id (user_id),
    INDEX idx_profiles_pesantren (pondok_pesantren),
    INDEX idx_profiles_status (status_santri)
);

-- Assessment configurations and metadata
CREATE TABLE assessment_configs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    assessment_code VARCHAR(20) NOT NULL UNIQUE,
    assessment_name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(10) DEFAULT '1.0',
    total_items INT NOT NULL,
    estimated_time_minutes INT DEFAULT 15,
    is_active BOOLEAN DEFAULT TRUE,
    requires_supervision BOOLEAN DEFAULT FALSE,
    age_min INT DEFAULT 12,
    age_max INT DEFAULT 30,
    cultural_context ENUM('general', 'islamic', 'indonesian', 'pesantren') DEFAULT 'pesantren',
    scoring_method ENUM('likert', 'boolean', 'mixed') NOT NULL,
    clinical_cutoffs JSON,
    psychometric_properties JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_assessment_code (assessment_code),
    INDEX idx_assessment_active (is_active)
);

-- Assessment sessions (each attempt)
CREATE TABLE assessment_sessions (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    assessment_code VARCHAR(20) NOT NULL,
    session_status ENUM('started', 'in_progress', 'completed', 'abandoned', 'invalid') DEFAULT 'started',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    duration_seconds INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_type ENUM('mobile', 'tablet', 'desktop') NULL,
    is_supervised BOOLEAN DEFAULT FALSE,
    supervisor_id VARCHAR(36) NULL,
    notes TEXT,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_code) REFERENCES assessment_configs(assessment_code),
    FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_sessions_user_id (user_id),
    INDEX idx_sessions_assessment (assessment_code),
    INDEX idx_sessions_status (session_status),
    INDEX idx_sessions_completed (completed_at)
);

-- Individual question responses
CREATE TABLE assessment_responses (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    session_id VARCHAR(36) NOT NULL,
    question_id VARCHAR(50) NOT NULL,
    question_text TEXT NOT NULL,
    response_value INT NULL,
    response_boolean BOOLEAN NULL,
    response_text TEXT NULL,
    response_time_seconds DECIMAL(10,2) NULL,
    is_reverse_scored BOOLEAN DEFAULT FALSE,
    domain VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    INDEX idx_responses_session (session_id),
    INDEX idx_responses_question (question_id),
    INDEX idx_responses_domain (domain)
);

-- Assessment results and interpretations
CREATE TABLE assessment_results (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    session_id VARCHAR(36) NOT NULL,
    assessment_code VARCHAR(20) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    
    total_raw_score DECIMAL(10,2) NULL,
    domain_scores JSON,
    
    total_t_score DECIMAL(5,2) NULL,
    total_percentile DECIMAL(5,2) NULL,
    domain_t_scores JSON,
    domain_percentiles JSON,
    
    overall_severity ENUM('normal', 'mild', 'moderate', 'severe', 'extremely_severe') NULL,
    domain_severities JSON,
    risk_level ENUM('low', 'moderate', 'high', 'crisis') DEFAULT 'low',
    
    interpretation_summary TEXT,
    clinical_recommendations JSON,
    referral_recommended BOOLEAN DEFAULT FALSE,
    follow_up_recommended BOOLEAN DEFAULT FALSE,
    follow_up_timeframe VARCHAR(50) NULL,
    
    reliability_alpha DECIMAL(5,3) NULL,
    response_consistency DECIMAL(5,3) NULL,
    completion_percentage DECIMAL(5,2) DEFAULT 100.00,
    validity_flags JSON,
    
    religious_coping_indicators JSON,
    cultural_considerations TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_code) REFERENCES assessment_configs(assessment_code),
    
    INDEX idx_results_session (session_id),
    INDEX idx_results_user (user_id),
    INDEX idx_results_assessment (assessment_code),
    INDEX idx_results_risk (risk_level),
    INDEX idx_results_severity (overall_severity),
    INDEX idx_results_date (created_at)
);

-- User progress tracking
CREATE TABLE user_progress (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    measurement_date DATE NOT NULL,
    assessment_session_id VARCHAR(36) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_session_id) REFERENCES assessment_sessions(id) ON DELETE SET NULL,
    
    INDEX idx_progress_user (user_id),
    INDEX idx_progress_metric (metric_name),
    INDEX idx_progress_date (measurement_date)
);

-- Crisis alerts and notifications
CREATE TABLE crisis_alerts (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    assessment_result_id VARCHAR(36) NOT NULL,
    alert_level ENUM('moderate', 'high', 'crisis', 'emergency') NOT NULL,
    trigger_criteria JSON,
    alert_message TEXT NOT NULL,
    is_acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by VARCHAR(36) NULL,
    acknowledged_at TIMESTAMP NULL,
    action_taken TEXT,
    resolution_status ENUM('pending', 'in_progress', 'resolved', 'escalated') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_result_id) REFERENCES assessment_results(id) ON DELETE CASCADE,
    FOREIGN KEY (acknowledged_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_alerts_user (user_id),
    INDEX idx_alerts_level (alert_level),
    INDEX idx_alerts_status (resolution_status),
    INDEX idx_alerts_date (created_at)
);

-- Counselor-student relationships
CREATE TABLE counselor_assignments (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    counselor_id VARCHAR(36) NOT NULL,
    student_id VARCHAR(36) NOT NULL,
    assignment_type ENUM('primary', 'secondary', 'supervisor', 'consultant') DEFAULT 'primary',
    assigned_date DATE NOT NULL,
    active_until DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (counselor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_primary_assignment (student_id, assignment_type, is_active),
    INDEX idx_assignments_counselor (counselor_id),
    INDEX idx_assignments_student (student_id),
    INDEX idx_assignments_active (is_active)
);

-- Educational content and progress
CREATE TABLE educational_modules (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    module_code VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content_type ENUM('video', 'article', 'interactive', 'game', 'quiz') NOT NULL,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    estimated_duration_minutes INT DEFAULT 10,
    prerequisites JSON,
    learning_objectives JSON,
    content_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    islamic_context BOOLEAN DEFAULT TRUE,
    target_audience ENUM('students', 'counselors', 'all') DEFAULT 'students',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_modules_code (module_code),
    INDEX idx_modules_type (content_type),
    INDEX idx_modules_active (is_active)
);

-- User educational progress
CREATE TABLE user_learning_progress (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    module_code VARCHAR(50) NOT NULL,
    progress_status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    time_spent_minutes INT DEFAULT 0,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    quiz_scores JSON,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (module_code) REFERENCES educational_modules(module_code),
    
    UNIQUE KEY unique_user_module (user_id, module_code),
    INDEX idx_learning_user (user_id),
    INDEX idx_learning_module (module_code),
    INDEX idx_learning_status (progress_status)
);

-- System logs and audit trail
CREATE TABLE audit_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(36) NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_resource (resource_type, resource_id),
    INDEX idx_audit_timestamp (timestamp)
);

-- Insert initial assessment configurations
INSERT INTO assessment_configs (
    assessment_code, assessment_name, description, total_items, estimated_time_minutes,
    scoring_method, clinical_cutoffs, psychometric_properties
) VALUES 
(
    'DASS42', 'Depression Anxiety Stress Scales-42',
    'Mengukur tingkat depresi, kecemasan, dan stres berdasarkan pengalaman 7 hari terakhir',
    42, 15, 'likert',
    JSON_OBJECT(
        'depression', JSON_OBJECT('normal', 9, 'mild', 13, 'moderate', 20, 'severe', 27),
        'anxiety', JSON_OBJECT('normal', 7, 'mild', 9, 'moderate', 14, 'severe', 19),
        'stress', JSON_OBJECT('normal', 14, 'mild', 18, 'moderate', 25, 'severe', 33)
    ),
    JSON_OBJECT('reliability', 0.91, 'validity', 'high', 'normative_sample', 'indonesian_students')
),
(
    'GSE', 'General Self-Efficacy Scale',
    'Mengukur keyakinan diri umum dalam menghadapi tantangan hidup',
    10, 8, 'likert',
    JSON_OBJECT(
        'total', JSON_OBJECT('low', 25, 'moderate', 32, 'high', 40)
    ),
    JSON_OBJECT('reliability', 0.86, 'validity', 'high')
),
(
    'MHKQ', 'Mental Health Knowledge Questionnaire',
    'Menilai tingkat pengetahuan tentang kesehatan mental dan cara penanganannya',
    20, 12, 'boolean',
    JSON_OBJECT(
        'total', JSON_OBJECT('low', 10, 'moderate', 15, 'high', 20)
    ),
    JSON_OBJECT('reliability', 0.78, 'validity', 'moderate')
),
(
    'MSPSS', 'Multidimensional Scale of Perceived Social Support',
    'Mengukur persepsi dukungan sosial dari keluarga, teman, dan figur penting',
    12, 10, 'likert',
    JSON_OBJECT(
        'total', JSON_OBJECT('low', 48, 'moderate', 68, 'high', 84),
        'family', JSON_OBJECT('low', 16, 'moderate', 22, 'high', 28),
        'friends', JSON_OBJECT('low', 16, 'moderate', 22, 'high', 28),
        'significant_other', JSON_OBJECT('low', 16, 'moderate', 22, 'high', 28)
    ),
    JSON_OBJECT('reliability', 0.88, 'validity', 'high')
),
(
    'PDD', 'Perceived Devaluation-Discrimination Scale',
    'Mengukur persepsi stigma dan diskriminasi terhadap masalah kesehatan mental',
    12, 8, 'likert',
    JSON_OBJECT(
        'total', JSON_OBJECT('low', 24, 'moderate', 36, 'high', 48)
    ),
    JSON_OBJECT('reliability', 0.82, 'validity', 'high')
),
(
    'SRQ20', 'Self-Reporting Questionnaire-20',
    'Skrining umum gangguan mental berdasarkan standar WHO',
    20, 10, 'boolean',
    JSON_OBJECT(
        'total', JSON_OBJECT('normal', 7, 'probable_disorder', 8)
    ),
    JSON_OBJECT('reliability', 0.85, 'validity', 'high', 'who_validated', true)
);

-- Insert sample educational modules
INSERT INTO educational_modules (
    module_code, title, description, content_type, difficulty_level,
    estimated_duration_minutes, learning_objectives, islamic_context
) VALUES
(
    'MH_BASICS', 'Dasar-dasar Kesehatan Mental dalam Islam',
    'Pengenalan konsep kesehatan mental dari perspektif Islam',
    'article', 'beginner', 15,
    JSON_ARRAY('Memahami definisi kesehatan mental', 'Mengetahui pandangan Islam tentang kesehatan jiwa'),
    true
),
(
    'STRESS_MGMT', 'Manajemen Stres dengan Pendekatan Islami',
    'Teknik-teknik mengelola stres menggunakan ajaran Islam',
    'interactive', 'intermediate', 20,
    JSON_ARRAY('Mengenali tanda-tanda stres', 'Menerapkan teknik relaksasi Islami'),
    true
),
(
    'PEER_SUPPORT', 'Dukungan Sesama Santri',
    'Cara memberikan dan mencari dukungan dari sesama santri',
    'video', 'beginner', 12,
    JSON_ARRAY('Memahami pentingnya dukungan sosial', 'Teknik komunikasi empatis'),
    true
);

-- Create views for common queries
CREATE OR REPLACE VIEW user_latest_assessments AS
SELECT 
    u.id as user_id,
    u.email,
    p.nama_lengkap,
    p.pondok_pesantren,
    ar.assessment_code,
    ar.total_raw_score,
    ar.overall_severity,
    ar.risk_level,
    ar.created_at as assessment_date,
    ROW_NUMBER() OVER (PARTITION BY u.id, ar.assessment_code ORDER BY ar.created_at DESC) as rn
FROM users u
JOIN profiles p ON u.id = p.user_id
JOIN assessment_results ar ON u.id = ar.user_id
WHERE ar.completion_percentage >= 80.0;

CREATE OR REPLACE VIEW high_risk_students AS
SELECT 
    u.id as user_id,
    u.email,
    p.nama_lengkap,
    p.pondok_pesantren,
    ar.assessment_code,
    ar.risk_level,
    ar.overall_severity,
    ar.created_at as assessment_date,
    ca.counselor_id
FROM users u
JOIN profiles p ON u.id = p.user_id
JOIN assessment_results ar ON u.id = ar.user_id
LEFT JOIN counselor_assignments ca ON u.id = ca.student_id AND ca.is_active = TRUE
WHERE ar.risk_level IN ('high', 'crisis')
    AND ar.created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY);