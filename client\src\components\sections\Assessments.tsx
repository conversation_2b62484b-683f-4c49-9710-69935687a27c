import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";

const Assessments = () => {
  const items = [
    { key: "MHKQ", title: "<PERSON>HK<PERSON> (Mental Health Knowledge)", link: "", ready: false },
    { key: "PDD", title: "PDD (Perceived Devaluation Discrimination)", link: "https://docs.google.com/document/d/1XHCibeQfIneNOLPaNmwMmnuceasnoeWh/edit?usp=sharing", ready: true },
    { key: "GSE", title: "GSE (General Self-Efficacy)", link: "", ready: false },
    { key: "MSCS", title: "MSCS (Mindful Self-Care Scale)", link: "", ready: false },
    { key: "SRQ20", title: "SRQ-20 (Self-Reporting Questionnaire)", link: "https://docs.google.com/document/d/1STjaeoqCylbdQShxkjg9FXYgzROvd1dW/edit?usp=sharing", ready: true },
    { key: "DASS42", title: "DASS-42 (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)", link: "https://docs.google.com/document/d/13ki5rVNE__H1Fd2UWU3eb_BaTyv6GRVT/edit?usp=sharing", ready: true },
  ];

  const handleStart = (it: (typeof items)[number]) => {
    if (it.link) {
      window.open(it.link, "_blank");
      return;
    }
    toast({
      title: `${it.title}`,
      description: "Link akan ditambahkan. Assessment in-app akan aktif setelah backend tersedia.",
    });
  };

  return (
    <section id="assessments" className="container mx-auto px-6 py-12 md:py-16">
      <h2 className="text-2xl md:text-3xl font-semibold">Assessment</h2>
      <p className="text-muted-foreground mt-2 max-w-prose">
        Mulai dari kuesioner berikut untuk mengenali kondisi Anda.
      </p>
      <div className="mt-8 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {items.map((it) => (
          <Card key={it.key} className="h-full flex flex-col">
            <CardHeader>
              <CardTitle className="text-lg">{it.title}</CardTitle>
            </CardHeader>
            <CardContent className="mt-auto">
              <Button variant={it.ready ? "default" : "soft"} onClick={() => handleStart(it)}>
                {it.ready ? "Buka Dokumen" : "Segera Tersedia"}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default Assessments;
