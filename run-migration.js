#!/usr/bin/env node

/**
 * 🔄 Database Migration Runner
 * 
 * Runs database migrations to add role-based access control
 */

import { ConnectionManager } from './server/database/connection-manager.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔄 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function runMigration() {
  logHeader('Database Migration - Adding Role-Based Access Control');
  
  try {
    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();
    
    logInfo(`Current provider: ${provider}`);
    
    if (provider === 'sqlite') {
      await runSQLiteMigration(connection);
    } else if (provider === 'aiven-mysql' || provider === 'mysql') {
      await runMySQLMigration(connection);
    } else if (provider === 'neon-postgresql') {
      await runPostgreSQLMigration(connection);
    } else {
      throw new Error(`Unsupported provider: ${provider}`);
    }
    
    logSuccess('Migration completed successfully!');
    
  } catch (error) {
    logError(`Migration failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

async function runSQLiteMigration(connection) {
  logInfo('Running SQLite migration...');
  
  const db = connection.db;
  
  try {
    // Check if role column already exists
    const tableInfo = db.prepare("PRAGMA table_info(users)").all();
    const hasRole = tableInfo.some(col => col.name === 'role');
    
    if (!hasRole) {
      logInfo('Adding role column to users table...');
      db.exec(`
        ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user' NOT NULL;
        ALTER TABLE users ADD COLUMN is_active INTEGER DEFAULT 1 NOT NULL;
        ALTER TABLE users ADD COLUMN last_login_at TEXT;
        ALTER TABLE users ADD COLUMN email_verified INTEGER DEFAULT 0 NOT NULL;
        ALTER TABLE users ADD COLUMN email_verified_at TEXT;
      `);
      
      // Create indexes
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
        CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
        CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
      `);
      
      logSuccess('User table updated with role-based fields');
    } else {
      logInfo('Role column already exists, skipping user table migration');
    }
    
    // Create admin user if not exists
    const adminExists = db.prepare("SELECT id FROM users WHERE email = ?").get('<EMAIL>');
    
    if (!adminExists) {
      logInfo('Creating default admin user...');
      const adminId = 'admin-' + Date.now();
      db.prepare(`
        INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
        VALUES (?, ?, ?, 'admin', 1, 1, datetime('now'), datetime('now'))
      `).run(adminId, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
      
      logSuccess('Default admin user created (email: <EMAIL>, password: admin123)');
    } else {
      logInfo('Admin user already exists');
    }
    
    // Create additional tables if they don't exist
    logInfo('Creating additional tables...');
    
    // User sessions table
    db.exec(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        session_token TEXT NOT NULL UNIQUE,
        expires_at TEXT NOT NULL,
        ip_address TEXT,
        user_agent TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
      
      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token);
      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);
      CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at);
    `);
    
    // User permissions table
    db.exec(`
      CREATE TABLE IF NOT EXISTS user_permissions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        permission TEXT NOT NULL,
        granted_by TEXT,
        granted_at TEXT DEFAULT CURRENT_TIMESTAMP,
        expires_at TEXT,
        is_active INTEGER DEFAULT 1,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE(user_id, permission)
      );
      
      CREATE INDEX IF NOT EXISTS idx_permissions_user ON user_permissions(user_id);
      CREATE INDEX IF NOT EXISTS idx_permissions_permission ON user_permissions(permission);
    `);
    
    // Audit logs table
    db.exec(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        action TEXT NOT NULL,
        resource_type TEXT,
        resource_id TEXT,
        old_values TEXT,
        new_values TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      );
      
      CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_logs(user_id);
      CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_logs(action);
      CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_logs(resource_type, resource_id);
      CREATE INDEX IF NOT EXISTS idx_audit_created ON audit_logs(created_at);
    `);
    
    logSuccess('Additional tables created successfully');
    
  } catch (error) {
    throw new Error(`SQLite migration failed: ${error.message}`);
  }
}

async function runMySQLMigration(connection) {
  logInfo('Running MySQL migration...');
  
  const pool = connection.getPool();
  
  try {
    // Check if role column already exists
    const [columns] = await pool.execute("SHOW COLUMNS FROM users LIKE 'role'");
    
    if (columns.length === 0) {
      logInfo('Adding role column to users table...');
      await pool.execute(`
        ALTER TABLE users 
        ADD COLUMN role VARCHAR(20) DEFAULT 'user' NOT NULL AFTER password,
        ADD COLUMN is_active BOOLEAN DEFAULT TRUE NOT NULL AFTER role,
        ADD COLUMN last_login_at TIMESTAMP NULL AFTER is_active,
        ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL AFTER last_login_at,
        ADD COLUMN email_verified_at TIMESTAMP NULL AFTER email_verified
      `);
      
      // Create indexes
      await pool.execute(`
        CREATE INDEX idx_users_role ON users(role);
        CREATE INDEX idx_users_active ON users(is_active);
        CREATE INDEX idx_users_email_verified ON users(email_verified);
      `);
      
      logSuccess('User table updated with role-based fields');
    } else {
      logInfo('Role column already exists, skipping user table migration');
    }
    
    // Create admin user if not exists
    const [adminExists] = await pool.execute("SELECT id FROM users WHERE email = ?", ['<EMAIL>']);
    
    if (adminExists.length === 0) {
      logInfo('Creating default admin user...');
      await pool.execute(`
        INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
        VALUES (UUID(), ?, ?, 'admin', TRUE, TRUE, NOW(), NOW())
      `, ['<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi']);
      
      logSuccess('Default admin user created (email: <EMAIL>, password: admin123)');
    } else {
      logInfo('Admin user already exists');
    }
    
    // Create additional tables
    logInfo('Creating additional tables...');
    
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        session_token VARCHAR(255) NOT NULL UNIQUE,
        expires_at TIMESTAMP NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_sessions_token (session_token),
        INDEX idx_sessions_user (user_id),
        INDEX idx_sessions_expires (expires_at)
      )
    `);
    
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS user_permissions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        permission VARCHAR(100) NOT NULL,
        granted_by VARCHAR(36),
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_user_permission (user_id, permission),
        INDEX idx_permissions_user (user_id),
        INDEX idx_permissions_permission (permission)
      )
    `);
    
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        action VARCHAR(100) NOT NULL,
        resource_type VARCHAR(50),
        resource_id VARCHAR(36),
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_audit_user (user_id),
        INDEX idx_audit_action (action),
        INDEX idx_audit_resource (resource_type, resource_id),
        INDEX idx_audit_created (created_at)
      )
    `);
    
    logSuccess('Additional tables created successfully');
    
  } catch (error) {
    throw new Error(`MySQL migration failed: ${error.message}`);
  }
}

async function runPostgreSQLMigration(connection) {
  logInfo('Running PostgreSQL migration...');
  
  const pool = connection.getPool();
  
  try {
    // Check if role column already exists
    const result = await pool.execute(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'role'
    `);
    
    if (result[0].length === 0) {
      logInfo('Adding role column to users table...');
      await pool.execute(`
        ALTER TABLE users 
        ADD COLUMN role VARCHAR(20) DEFAULT 'user' NOT NULL,
        ADD COLUMN is_active BOOLEAN DEFAULT TRUE NOT NULL,
        ADD COLUMN last_login_at TIMESTAMP,
        ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL,
        ADD COLUMN email_verified_at TIMESTAMP
      `);
      
      // Create indexes
      await pool.execute(`
        CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
        CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
        CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
      `);
      
      logSuccess('User table updated with role-based fields');
    } else {
      logInfo('Role column already exists, skipping user table migration');
    }
    
    // Create admin user if not exists
    const [adminExists] = await pool.execute("SELECT id FROM users WHERE email = $1", ['<EMAIL>']);
    
    if (adminExists.length === 0) {
      logInfo('Creating default admin user...');
      await pool.execute(`
        INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
        VALUES (uuid_generate_v4(), $1, $2, 'admin', TRUE, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, ['<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi']);
      
      logSuccess('Default admin user created (email: <EMAIL>, password: admin123)');
    } else {
      logInfo('Admin user already exists');
    }
    
    logSuccess('PostgreSQL migration completed');
    
  } catch (error) {
    throw new Error(`PostgreSQL migration failed: ${error.message}`);
  }
}

// Run migration
runMigration();
