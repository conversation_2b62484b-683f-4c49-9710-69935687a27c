import { use<PERSON>emo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Button } from "@/components/ui/button";

export type MediaKind = "game" | "pdf" | "video" | "iframe";

function toDrivePreview(url: string) {
  // Convert shared GDrive link to embeddable preview
  try {
    const u = new URL(url);
    if (u.hostname.includes("drive.google.com") && u.pathname.includes("/file/")) {
      const parts = u.pathname.split("/");
      const id = parts.find((p) => p.length > 20) || "";
      return `https://drive.google.com/file/d/${id}/preview`;
    }
    return url;
  } catch {
    return url;
  }
}

function toDriveVideoUrl(url: string) {
  // Convert GDrive link to direct video URL for HTML5 video element
  try {
    const u = new URL(url);
    if (u.hostname.includes("drive.google.com") && u.pathname.includes("/file/")) {
      const parts = u.pathname.split("/");
      const id = parts.find((p) => p.length > 20) || "";
      return `https://drive.google.com/uc?export=download&id=${id}`;
    }
    return url;
  } catch {
    return url;
  }
}

function getEmbedUrl(url: string, kind: MediaKind) {
  if (kind === "pdf") return toDrivePreview(url);
  if (kind === "video") return toDriveVideoUrl(url);
  return url; // game/iframe direct
}

type Props = {
  title: string;
  url: string;
  kind: MediaKind;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (o: boolean) => void;
};

const MediaLightbox = ({ title, url, kind, trigger, open, onOpenChange }: Props) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const isControlled = typeof open === "boolean" && typeof onOpenChange === "function";
  const o = isControlled ? open! : internalOpen;
  const setO = isControlled ? onOpenChange! : setInternalOpen;

  const embedUrl = useMemo(() => getEmbedUrl(url, kind), [url, kind]);

  const renderContent = () => {
    if (kind === "video") {
      return (
        <AspectRatio ratio={16 / 9} className="rounded-md overflow-hidden border bg-black">
          <video
            controls
            className="w-full h-full object-contain"
            preload="metadata"
          >
            <source src={embedUrl} type="video/mp4" />
            <p className="text-white p-4">
              Browser Anda tidak mendukung video HTML5. 
              <a href={embedUrl} className="text-blue-400 underline ml-1">
                Unduh video
              </a>
            </p>
          </video>
        </AspectRatio>
      );
    }

    if (kind === "pdf") {
      return (
        <div className="w-full" style={{ height: "70vh" }}>
          <iframe
            title={title}
            src={embedUrl}
            className="w-full h-full rounded-md border"
            loading="lazy"
          />
        </div>
      );
    }

    // For games and other iframes
    const aspectRatio = kind === "game" || kind === "iframe" ? 3 / 2 : 16 / 9;
    return (
      <AspectRatio ratio={aspectRatio} className="rounded-md overflow-hidden border">
        <iframe
          title={title}
          src={embedUrl}
          className="w-full h-full"
          loading="lazy"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen"
          allowFullScreen
        />
      </AspectRatio>
    );
  };

  return (
    <Dialog open={o} onOpenChange={setO}>
      {trigger}
      <DialogContent className="sm:max-w-5xl w-[95vw] max-h-[90vh] p-0 overflow-hidden">
        <DialogHeader className="px-4 pt-4 pb-2 shrink-0">
          <DialogTitle className="text-base sm:text-lg">{title}</DialogTitle>
        </DialogHeader>
        <div className="px-4 pb-4 overflow-auto">
          {renderContent()}
          <div className="mt-3 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <span className="text-xs text-muted-foreground truncate max-w-full sm:max-w-none">
              {kind === "video" ? url : embedUrl}
            </span>
            <Button asChild variant="outline" size="sm" className="shrink-0">
              <a href={kind === "video" ? url : embedUrl} target="_blank" rel="noreferrer">
                Buka di Tab Baru
              </a>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MediaLightbox;
