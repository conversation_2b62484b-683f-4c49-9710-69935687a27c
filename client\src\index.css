@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    /* Fresh Green Brand Palette from Logo */
    --primary: 108 60% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 60 20% 88%;
    --secondary-foreground: 108 25% 25%;

    --muted: 60 15% 92%;
    --muted-foreground: 108 15% 50%;

    --accent: 100 45% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 108 60% 45%;

    --radius: 0.75rem;

    /* Brand tokens - Fresh Green Theme */
    --brand: 108 60% 45%;
    --brand-2: 100 45% 40%;
    --primary-glow: 108 60% 55%;

    /* Gradients & Shadows */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand)) 0%, hsl(var(--brand-2)) 100%);
    --gradient-surface: radial-gradient(800px 400px at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--brand) / 0.08), transparent 60%);

    --shadow-elegant: 0 10px 30px -12px hsl(var(--brand) / 0.25);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);

    /* Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: var(--brand);
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 108 50% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 108 15% 20%;
    --secondary-foreground: 60 20% 90%;

    --muted: 108 10% 25%;
    --muted-foreground: 108 20% 70%;

    --accent: 100 40% 45%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 92% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Gradients & Shadows */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-2)) 0%, hsl(var(--brand)) 100%);
    --gradient-surface: radial-gradient(800px 400px at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--brand) / 0.12), transparent 60%);
    --shadow-elegant: 0 10px 30px -12px hsl(var(--brand) / 0.3);
    --shadow-glow: 0 0 50px hsl(var(--primary-glow) / 0.5);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bg-hero {
    background-image: var(--gradient-hero);
  }
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }
  .interactive-surface {
    position: relative;
    overflow: hidden;
  }
  .interactive-surface::after {
    content: "";
    position: absolute;
    inset: -1px;
    background: var(--gradient-surface);
    pointer-events: none;
    transition: opacity 0.3s ease;
    opacity: 0;
  }
  .interactive-surface:hover::after {
    opacity: 1;
  }
}

@media (prefers-reduced-motion: reduce) {
  .interactive-surface::after {
    transition: none;
  }
}
