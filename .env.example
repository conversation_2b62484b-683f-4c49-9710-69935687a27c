# Database Configuration
DATABASE_URL="mysql://root:password@localhost:3306/santrimental6"

# Database Provider Selection (auto-detected if not specified)
# Options: mysql, aiven-mysql, neon-postgresql, sqlite
DATABASE_PROVIDER="mysql"

# AIVEN MySQL Configuration
AIVEN_MYSQL_URL="mysql://username:password@hostname:port/database?ssl-mode=REQUIRED"
AIVEN_CA_CERT="-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"

# NEON PostgreSQL Configuration
NEON_DATABASE_URL="****************************************************************"

# SQLite Configuration (fallback)
SQLITE_PATH="./data/santrimental.db"

# Database Provider Selection
# Options: mysql, aiven-mysql, postgresql, neon-postgresql, sqlite
DATABASE_PROVIDER="mysql"

# AIVEN MySQL Configuration
AIVEN_MYSQL_URL="mysql://avnadmin:<EMAIL>:17998/defaultdb?ssl-mode=REQUIRED"
AIVEN_CA_CERT=""

# NEON PostgreSQL Configuration
NEON_DATABASE_URL=""

# App Settings
NODE_ENV="development"
PORT=5000
FRONTEND_URL="http://localhost:5000"

# JWT Secret
JWT_SECRET="f5fe56f12ff535e570f04a7fa1f799441459f54c283f9d87893192cd99d007a8"

# Offline Mode (optional)
ENABLE_OFFLINE=true

# Example for production:
# DATABASE_URL="mysql://username:password@hostname:3306/database_name"
# FRONTEND_URL="https://yourdomain.com"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-secure-app-password"





