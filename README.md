# 🧠 SantriMental - Platform Kesehatan Mental Pesantren

Platform assessment dan edukasi kesehatan mental yang dirancang khusus untuk santri pesantren dengan dukungan **offline-first** dan **auto-sync**.

## ✨ Fitur Utama

### 🎯 **Assessment Tools**
- ✅ DASS-42 (Depression, Anxiety, Stress Scale)
- ✅ GSE (General Self-Efficacy Scale)
- ✅ MHKQ (Mental Health Knowledge Questionnaire)
- ✅ MSPSS (Multidimensional Scale of Perceived Social Support)
- ✅ PDD (Perceived Devaluation-Discrimination Scale)
- ✅ SRQ-20 (Self-Reporting Questionnaire)

### 📱 **Mobile-First PWA**
- ✅ Progressive Web App (PWA)
- ✅ Offline-first architecture
- ✅ Auto-sync saat online
- ✅ Install di home screen
- ✅ Background sync
- ✅ Service worker caching

### 🔄 **Offline & Sync**
- ✅ SQLite untuk storage offline
- ✅ MySQL untuk server database
- ✅ Auto-sync setiap 30 detik
- ✅ Sync indicator real-time
- ✅ Manual sync trigger
- ✅ Conflict resolution

### 🏫 **Pesantren Context**
- ✅ Islamic cultural considerations
- ✅ Religious coping indicators
- ✅ Pesantren-specific profiles
- ✅ Bahasa Indonesia interface

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher)
- **MySQL** (v5.6+ or v8.0+)
- **npm** or **yarn**

### 1. Database Setup

#### MySQL Database
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE santrimental6 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Import schema
mysql -u root -p santrimental6 < database/schema.sql
```

2. **Configure Environment**
   Create `.env` file:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=your_password
   DB_NAME=santrimental6
   DB_PORT=3306
   ```

3. **Run Setup Script**
   ```bash
   node database/setup-mysql.js
   ```

### 2. Environment Configuration

Create `.env` file in the root directory:

```env
# Database (MySQL for production, optional for offline mode)
DATABASE_URL="mysql://root:password@localhost:3306/santrimental6"

# App Settings
NODE_ENV="development"
PORT=5000

# Offline Mode (optional)
ENABLE_OFFLINE=true
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Start Development Server

#### Online Mode (with MySQL)
```bash
npm run dev
```

#### Offline Mode (with SQLite)
```bash
npm run dev:offline
```

The application will be available at `http://localhost:5000`

### 5. Build for Production

```bash
# Standard build
npm run build

# Build for cPanel deployment
npm run build:cpanel
```

## 🌐 Deployment

### cPanel Shared Hosting

1. **Build aplikasi**
   ```bash
   npm run build:cpanel
   ```

2. **Upload ke cPanel**
   - Upload `santrimental-deploy.tar.gz`
   - Extract di public_html atau subdomain folder

3. **Setup Node.js App**
   - App Root: `/public_html`
   - Startup File: `dist/index.js`
   - Environment: `NODE_ENV=production`

4. **Konfigurasi Database**
   - Buat database MySQL di cPanel
   - Import `database/schema.sql`
   - Update `DATABASE_URL` di environment

📖 **Panduan lengkap**: [deploy-cpanel.md](./deploy-cpanel.md)

## 📱 PWA Features

### Installation
1. Buka aplikasi di mobile browser
2. Tap "Add to Home Screen" atau "Install App"
3. Aplikasi akan tersedia seperti native app

### Offline Functionality
- ✅ Isi assessment tanpa internet
- ✅ Lihat riwayat assessment tersimpan
- ✅ Baca materi edukasi yang sudah diunduh
- ✅ Data otomatis sync saat online

### Sync Indicator
- 🟢 **Hijau**: Tersinkronisasi
- 🟡 **Kuning**: Ada data pending sync
- 🔴 **Merah**: Offline mode
- 🔄 **Biru**: Sedang sinkronisasi

## 🔄 Offline & Sync Architecture

### Client-Side (IndexedDB)
```javascript
// Offline storage dengan Dexie
import { offlineStorage } from './lib/offline-db';

// Simpan data offline
await offlineStorage.createAssessmentResult(data);

// Auto-sync saat online
offlineStorage.syncToServer();
```

### Server-Side (SQLite + MySQL)
```javascript
// Dual database support
import { OfflineStorage } from './server/offline-storage';

// SQLite untuk offline, MySQL untuk production
const storage = new OfflineStorage();
```

### Sync Process
1. **Offline**: Data disimpan di SQLite/IndexedDB
2. **Online**: Data masuk sync queue
3. **Background**: Auto-sync setiap 30 detik
4. **Manual**: Trigger sync via UI

## 🧪 Testing

### API Testing
```bash
# Test user registration
node test-api.js

# Test assessment flow
node test-assessment.js
```

### Offline Testing
1. Matikan internet/WiFi
2. Isi assessment di aplikasi
3. Nyalakan internet
4. Cek sync indicator berubah hijau

### PWA Testing
1. Audit dengan Lighthouse
2. Test install prompt
3. Test offline functionality
4. Test background sync

## 📁 Project Structure

```
santri-mental/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── ...
├── components/            # React components
├── lib/                   # Utility functions
├── database/              # Database files
│   ├── mysql-schema.sql   # MySQL schema
│   └── setup-mysql.js     # Setup script
├── shared/                # Shared types and schemas
└── public/               # Static assets
```

## 🔧 Database Schema

### Core Tables
- **users**: User authentication and roles
- **profiles**: Detailed user profiles for santri
- **assessment_configs**: Assessment configurations
- **assessment_sessions**: Individual assessment attempts
- **assessment_results**: Assessment results and interpretations
- **crisis_alerts**: Crisis detection and alerts

### Assessment Tools
The platform includes 6 validated assessment tools:
- **DASS42**: Depression, Anxiety, Stress Scale
- **GSE**: General Self-Efficacy Scale
- **MHKQ**: Mental Health Knowledge Questionnaire
- **MSPSS**: Multidimensional Scale of Perceived Social Support
- **PDD**: Perceived Devaluation-Discrimination Scale
- **SRQ20**: Self-Reporting Questionnaire (WHO standard)

## 🎯 Features

### For Students (Santri)
- **Self-Assessment**: Complete mental health assessments
- **Progress Tracking**: Monitor mental health over time
- **Educational Content**: Access Islamic-based mental health resources
- **Crisis Support**: Get immediate help when needed

### For Counselors
- **Student Dashboard**: View assigned students and their status
- **Assessment Results**: Detailed analysis and interpretations
- **Crisis Alerts**: Real-time notifications for high-risk students
- **Progress Monitoring**: Track student improvement over time

### For Administrators
- **User Management**: Manage students, counselors, and admins
- **System Analytics**: Overall platform usage and insights
- **Content Management**: Manage educational modules
- **Crisis Management**: Monitor and respond to crisis situations

## 🔐 Authentication

The platform uses NextAuth.js with credentials-based authentication:
- **Students**: Register with email and student ID
- **Counselors**: Admin-assigned accounts
- **Admins**: System administrators

## 📊 Assessment Process

1. **Registration**: Students create accounts with pesantren details
2. **Assessment**: Complete validated mental health assessments
3. **Analysis**: Automatic scoring and risk assessment
4. **Interpretation**: Islamic-contextualized results
5. **Support**: Connect with counselors if needed
6. **Follow-up**: Track progress over time

## 🚨 Crisis Detection

The system automatically detects crisis situations based on:
- Assessment scores above clinical thresholds
- Risk indicators in responses
- Pattern analysis over time
- Self-harm indicators

## 📱 Responsive Design

The platform is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- Low-bandwidth environments

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev

# Build
npm run build

# Start production
npm start

# Database migrations
npm run db:push
npm run db:studio
```

### Adding New Assessments

1. Add configuration to `assessment_configs` table
2. Create assessment questions in your frontend
3. Add scoring logic in the API
4. Update result interpretation

### Customizing for Your Pesantren

1. Update pesantren-specific content in profiles
2. Customize assessment cultural context
3. Add local counselor information
4. Configure crisis response protocols

## 🔍 Troubleshooting

### Database Connection Issues
```bash
# Check MySQL service
sudo systemctl status mysql

# Test connection
mysql -u root -p -e "SELECT 1"

# Check database exists
mysql -u root -p -e "SHOW DATABASES LIKE 'santrimental6'"
```

### Common Issues

1. **"Access denied for user"**
   - Check MySQL credentials in `.env`
   - Ensure user has proper permissions

2. **"Database not found"**
   - Run database setup script
   - Verify database name matches `.env`

3. **"Table doesn't exist"**
   - Run schema import again
   - Check for SQL errors during setup

## 📞 Support

For technical support:
- Check the troubleshooting section
- Review MySQL logs: `/var/log/mysql/error.log`
- Check application logs in development mode

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Islamic Psychology Research Center
- Indonesian Mental Health Association
- Pesantren Mental Health Initiative
- WHO Mental Health Guidelines