import { mysqlTable, text, serial, int, boolean, datetime, json, varchar, timestamp } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { sql } from "drizzle-orm";

export const users = mysqlTable("users", {
  id: varchar("id", { length: 36 }).primaryKey(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  password: text("password").notNull(),
  role: varchar("role", { length: 20 }).default("user").notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  lastLoginAt: timestamp("last_login_at"),
  emailVerified: boolean("email_verified").default(false).notNull(),
  emailVerifiedAt: timestamp("email_verified_at"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`).notNull(),
});

export const profiles = mysqlTable("profiles", {
  id: varchar("id", { length: 36 }).primaryKey(),
  userId: varchar("user_id", { length: 36 }).notNull().references(() => users.id),
  namaLengkap: varchar("nama_lengkap", { length: 255 }).notNull(),
  nomorInduk: varchar("nomor_induk", { length: 50 }),
  jenisKelamin: varchar("jenis_kelamin", { length: 10 }),
  tanggalLahir: varchar("tanggal_lahir", { length: 20 }),
  kelas: varchar("kelas", { length: 20 }),
  pondokPesantren: varchar("pondok_pesantren", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`).notNull(),
});

// Assessment configurations
export const assessmentConfigs = mysqlTable("assessment_configs", {
  id: varchar("id", { length: 36 }).primaryKey(),
  assessmentCode: varchar("assessment_code", { length: 20 }).notNull().unique(),
  assessmentName: varchar("assessment_name", { length: 255 }).notNull(),
  description: text("description"),
  version: varchar("version", { length: 10 }).default("1.0"),
  totalItems: int("total_items").notNull(),
  estimatedTimeMinutes: int("estimated_time_minutes").default(15),
  isActive: boolean("is_active").default(true),
  requiresSupervision: boolean("requires_supervision").default(false),
  ageMin: int("age_min").default(12),
  ageMax: int("age_max").default(30),
  culturalContext: varchar("cultural_context", { length: 20 }).default("pesantren"),
  scoringMethod: varchar("scoring_method", { length: 20 }).notNull(),
  clinicalCutoffs: text("clinical_cutoffs"),
  psychometricProperties: text("psychometric_properties"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`).notNull(),
});

// Assessment sessions
export const assessmentSessions = mysqlTable("assessment_sessions", {
  id: varchar("id", { length: 36 }).primaryKey(),
  userId: varchar("user_id", { length: 36 }).notNull().references(() => users.id),
  assessmentCode: varchar("assessment_code", { length: 20 }).notNull(),
  sessionStatus: varchar("session_status", { length: 20 }).default("started"),
  startedAt: timestamp("started_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  completedAt: timestamp("completed_at"),
  durationSeconds: int("duration_seconds"),
  ipAddress: varchar("ip_address", { length: 45 }),
  userAgent: text("user_agent"),
  deviceType: varchar("device_type", { length: 20 }),
  isSupervised: boolean("is_supervised").default(false),
  supervisorId: varchar("supervisor_id", { length: 36 }),
  notes: text("notes"),
});

// Assessment results
export const assessmentResults = mysqlTable("assessment_results", {
  id: varchar("id", { length: 36 }).primaryKey(),
  sessionId: varchar("session_id", { length: 36 }).notNull(),
  assessmentCode: varchar("assessment_code", { length: 20 }).notNull(),
  userId: varchar("user_id", { length: 36 }).notNull().references(() => users.id),
  totalRawScore: varchar("total_raw_score", { length: 10 }),
  domainScores: text("domain_scores"),
  totalTScore: varchar("total_t_score", { length: 10 }),
  totalPercentile: varchar("total_percentile", { length: 10 }),
  domainTScores: text("domain_t_scores"),
  domainPercentiles: text("domain_percentiles"),
  overallSeverity: varchar("overall_severity", { length: 20 }),
  domainSeverities: text("domain_severities"),
  riskLevel: varchar("risk_level", { length: 20 }).default("low"),
  interpretationSummary: text("interpretation_summary"),
  clinicalRecommendations: text("clinical_recommendations"),
  referralRecommended: boolean("referral_recommended").default(false),
  followUpRecommended: boolean("follow_up_recommended").default(false),
  followUpTimeframe: varchar("follow_up_timeframe", { length: 50 }),
  reliabilityAlpha: varchar("reliability_alpha", { length: 10 }),
  responseConsistency: varchar("response_consistency", { length: 10 }),
  completionPercentage: varchar("completion_percentage", { length: 10 }).default("100.00"),
  validityFlags: text("validity_flags"),
  religiousCopingIndicators: text("religious_coping_indicators"),
  culturalConsiderations: text("cultural_considerations"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`).notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  email: true,
  password: true,
});

export const insertProfileSchema = createInsertSchema(profiles).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAssessmentSessionSchema = createInsertSchema(assessmentSessions).omit({
  id: true,
});

export const insertAssessmentResultSchema = createInsertSchema(assessmentResults).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type Profile = typeof profiles.$inferSelect;
export type InsertProfile = z.infer<typeof insertProfileSchema>;
export type AssessmentConfig = typeof assessmentConfigs.$inferSelect;
export type AssessmentSession = typeof assessmentSessions.$inferSelect;
export type AssessmentResult = typeof assessmentResults.$inferSelect;
export type InsertAssessmentSession = z.infer<typeof insertAssessmentSessionSchema>;
export type InsertAssessmentResult = z.infer<typeof insertAssessmentResultSchema>;
