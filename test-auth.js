#!/usr/bin/env node

/**
 * 🔐 Authentication Testing Script
 * 
 * Tests user registration, login, and admin access
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔐 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test admin login
async function testAdminLogin() {
  logHeader('Testing Admin Login');
  
  try {
    logInfo('Attempting admin login...');
    
    const response = await fetch(`${BASE_URL}/api/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      logSuccess('Admin login successful!');
      logInfo(`User: ${data.data.user.email}`);
      logInfo(`Role: ${data.data.user.role}`);
      logInfo(`Name: ${data.data.user.namaLengkap}`);
      
      return data.data.token;
    } else {
      logError(`Admin login failed: ${data.error}`);
      return null;
    }
    
  } catch (error) {
    logError(`Admin login error: ${error.message}`);
    return null;
  }
}

// Test user registration
async function testUserRegistration() {
  logHeader('Testing User Registration');
  
  try {
    logInfo('Attempting user registration...');
    
    const testUser = {
      email: `test${Date.now()}@example.com`,
      password: 'testpassword123',
      namaLengkap: 'Test User',
      role: 'user'
    };
    
    const response = await fetch(`${BASE_URL}/api/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      logSuccess('User registration successful!');
      logInfo(`User: ${data.data.user.email}`);
      logInfo(`Role: ${data.data.user.role}`);
      logInfo(`Name: ${data.data.user.namaLengkap}`);
      
      return { user: testUser, token: data.data.token };
    } else {
      logError(`User registration failed: ${data.error}`);
      return null;
    }
    
  } catch (error) {
    logError(`User registration error: ${error.message}`);
    return null;
  }
}

// Test admin dashboard access
async function testAdminDashboard(token) {
  logHeader('Testing Admin Dashboard Access');
  
  try {
    logInfo('Attempting to access admin dashboard...');
    
    const response = await fetch(`${BASE_URL}/api/admin-dashboard/dashboard`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      logSuccess('Admin dashboard access successful!');
      logInfo(`Total Users: ${data.data.stats.totalUsers}`);
      logInfo(`Active Users: ${data.data.stats.activeUsers}`);
      logInfo(`Database Provider: ${data.data.systemInfo.currentProvider}`);
      
      return true;
    } else {
      logError(`Admin dashboard access failed: ${data.error}`);
      return false;
    }
    
  } catch (error) {
    logError(`Admin dashboard access error: ${error.message}`);
    return false;
  }
}

// Test user profile access
async function testUserProfile(token) {
  logHeader('Testing User Profile Access');
  
  try {
    logInfo('Attempting to access user profile...');
    
    const response = await fetch(`${BASE_URL}/api/auth/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      logSuccess('User profile access successful!');
      logInfo(`User: ${data.data.email}`);
      logInfo(`Role: ${data.data.role}`);
      
      return true;
    } else {
      logError(`User profile access failed: ${data.error}`);
      return false;
    }
    
  } catch (error) {
    logError(`User profile access error: ${error.message}`);
    return false;
  }
}

// Test unauthorized admin access
async function testUnauthorizedAdminAccess(userToken) {
  logHeader('Testing Unauthorized Admin Access');
  
  try {
    logInfo('Attempting unauthorized admin dashboard access...');
    
    const response = await fetch(`${BASE_URL}/api/admin-dashboard/dashboard`, {
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (response.status === 403) {
      logSuccess('Unauthorized access properly blocked!');
      logInfo(`Error: ${data.error}`);
      return true;
    } else {
      logError('Unauthorized access was not blocked!');
      return false;
    }
    
  } catch (error) {
    logError(`Unauthorized access test error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runAuthTests() {
  logHeader('Authentication & Authorization Testing Suite');
  
  const results = {
    adminLogin: false,
    userRegistration: false,
    adminDashboard: false,
    userProfile: false,
    unauthorizedAccess: false
  };
  
  // Test 1: Admin Login
  const adminToken = await testAdminLogin();
  results.adminLogin = Boolean(adminToken);
  
  // Test 2: User Registration
  const userResult = await testUserRegistration();
  results.userRegistration = Boolean(userResult);
  
  // Test 3: Admin Dashboard Access (with admin token)
  if (adminToken) {
    results.adminDashboard = await testAdminDashboard(adminToken);
  }
  
  // Test 4: User Profile Access (with user token)
  if (userResult) {
    results.userProfile = await testUserProfile(userResult.token);
  }
  
  // Test 5: Unauthorized Admin Access (with user token)
  if (userResult) {
    results.unauthorizedAccess = await testUnauthorizedAdminAccess(userResult.token);
  }
  
  // Summary
  logHeader('Test Results Summary');
  
  const testNames = {
    adminLogin: 'Admin Login',
    userRegistration: 'User Registration',
    adminDashboard: 'Admin Dashboard Access',
    userProfile: 'User Profile Access',
    unauthorizedAccess: 'Unauthorized Access Prevention'
  };
  
  let passed = 0;
  let total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, result]) => {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${testNames[test]}: ${status}`);
    if (result) passed++;
  });
  
  console.log('\n' + '='.repeat(60));
  console.log(`📈 Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    logSuccess('🎉 All authentication tests PASSED! System is secure and functional.');
  } else {
    logError('⚠️  Some tests failed. Please check the results above.');
  }
  
  console.log('='.repeat(60));
}

// Run the test suite
runAuthTests();
