#!/usr/bin/env node

/**
 * 🔍 Simple AIVEN MySQL Test with node-fetch
 * 
 * Direct test of AIVEN MySQL connection as requested
 */

import fetch from 'node-fetch';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

const testConnection = async () => {
  console.log('Testing Aiven MySQL connection...');
  
  try {
    log('🔍 Sending request to test-connections endpoint...', 'blue');
    
    const response = await fetch('http://localhost:5000/api/database/test-connections', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    log(`📡 Response status: ${response.status} ${response.statusText}`, 'blue');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('\n📊 Test Result:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success && result.data.results['aiven-mysql']) {
      log('\n✅ Aiven MySQL connection successful!', 'green');
      
      // Show current provider
      if (result.data.currentProvider) {
        log(`🎯 Current provider: ${result.data.currentProvider}`, 'blue');
      }
      
      // Show all results
      console.log('\n📋 All provider results:');
      Object.entries(result.data.results).forEach(([provider, status]) => {
        if (status) {
          log(`  ✅ ${provider}: Connected`, 'green');
        } else {
          log(`  ❌ ${provider}: Failed`, 'red');
        }
      });
      
    } else {
      log('\n❌ Aiven MySQL connection failed.', 'red');
      if (result.error) {
        log(`Error details: ${result.error}`, 'red');
      }
    }
    
  } catch (error) {
    log('❌ An error occurred while testing the connection:', 'red');
    
    if (error.code === 'ECONNREFUSED') {
      log('Connection refused - Server is not running', 'red');
      log('Please start the server with: npm run dev', 'yellow');
    } else if (error.message.includes('404')) {
      log('Endpoint not found - Database API may not be available', 'red');
      log('Make sure the full server is running with database routes', 'yellow');
    } else {
      log(`Error: ${error.message}`, 'red');
    }
  }
};

// Test database status endpoint
const testDatabaseStatus = async () => {
  console.log('\n🔍 Testing database status endpoint...');
  
  try {
    const response = await fetch('http://localhost:5000/api/database/status');
    
    if (response.ok) {
      const result = await response.json();
      console.log('Database Status:');
      console.log(JSON.stringify(result, null, 2));
      return true;
    } else {
      log(`Database status endpoint returned: ${response.status}`, 'yellow');
      return false;
    }
  } catch (error) {
    log(`Database status error: ${error.message}`, 'yellow');
    return false;
  }
};

// Main execution
const main = async () => {
  log('🚀 Starting AIVEN MySQL Connection Test', 'cyan');
  log('=' .repeat(50), 'cyan');
  
  // Test database status first
  const statusOk = await testDatabaseStatus();
  
  // Test connections
  await testConnection();
  
  log('\n' + '='.repeat(50), 'cyan');
  log('🎉 Test completed!', 'cyan');
};

main().catch(error => {
  log(`Test failed: ${error.message}`, 'red');
  process.exit(1);
});
