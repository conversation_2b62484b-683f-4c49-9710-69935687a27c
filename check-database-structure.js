#!/usr/bin/env node

/**
 * 🔍 Database Structure Checker
 * 
 * Checks current database structure
 */

import 'dotenv/config';
import mysql from 'mysql2/promise';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔍 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function checkDatabaseStructure() {
  logHeader('Database Structure Check');
  
  try {
    const url = process.env.AIVEN_MYSQL_URL || process.env.DATABASE_URL;
    if (!url) {
      throw new Error('No database URL found');
    }
    
    logInfo(`Connecting to database...`);
    const connection = await mysql.createConnection(url);
    
    // Show all tables
    logInfo('Checking tables...');
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('\n📋 Tables:');
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`  - ${tableName}`);
    });
    
    // Check users table structure
    if (tables.some(table => Object.values(table)[0] === 'users')) {
      logInfo('\nChecking users table structure...');
      const [columns] = await connection.execute('DESCRIBE users');
      
      console.log('\n👤 Users table columns:');
      columns.forEach(col => {
        console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
      });
      
      // Check if we have any users
      const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
      logInfo(`\nTotal users: ${userCount[0].count}`);
      
      if (userCount[0].count > 0) {
        const [users] = await connection.execute('SELECT id, email, role FROM users LIMIT 5');
        console.log('\n👥 Sample users:');
        users.forEach(user => {
          console.log(`  - ${user.email} (${user.role || 'no role'})`);
        });
      }
    }
    
    // Check profiles table structure
    if (tables.some(table => Object.values(table)[0] === 'profiles')) {
      logInfo('\nChecking profiles table structure...');
      const [columns] = await connection.execute('DESCRIBE profiles');
      
      console.log('\n📝 Profiles table columns:');
      columns.forEach(col => {
        console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
      });
    }
    
    await connection.end();
    logSuccess('Database structure check completed');
    
  } catch (error) {
    logError(`Check failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run check
checkDatabaseStructure();
