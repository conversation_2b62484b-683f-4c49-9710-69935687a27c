import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/sections/Navbar';
import { Footer } from '@/components/sections/Footer';
import { Download as DownloadIcon, Search, FileText, Calendar, User, Star, Eye, Heart } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Material {
  id: string;
  title: string;
  description: string;
  author: string;
  category: string;
  fileSize: string;
  downloadCount: string;
  rating: number;
  uploadDate: string;
  thumbnail: string;
  downloadUrl: string;
  tags: string[];
  preview?: string;
}

const materials: Material[] = [
  {
    id: '1',
    title: 'Panduan Lengkap Kesehatan Mental Santri',
    description: 'Buku panduan komprehensif tentang menjaga kesehatan mental dalam lingkungan pesantren dengan pendekatan Islami.',
    author: '<PERSON><PERSON> <PERSON>, <PERSON><PERSON>',
    category: 'Panduan',
    fileSize: '2.5 MB',
    downloadCount: '1.2K',
    rating: 4.8,
    uploadDate: '2024-01-15',
    thumbnail: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=400&fit=crop',
    downloadUrl: '/downloads/panduan-kesehatan-mental-santri.pdf',
    tags: ['kesehatan mental', 'panduan', 'santri', 'islami'],
    preview: 'Bab 1: Pengenalan Kesehatan Mental dalam Islam...'
  },
  {
    id: '2',
    title: 'Teknik Manajemen Stres untuk Pelajar',
    description: 'Modul praktis berisi berbagai teknik dan strategi mengelola stres dalam kehidupan akademik dan sosial.',
    author: 'Prof. Siti Nurhaliza, Ph.D',
    category: 'Modul',
    fileSize: '1.8 MB',
    downloadCount: '856',
    rating: 4.6,
    uploadDate: '2024-02-03',
    thumbnail: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=300&h=400&fit=crop',
    downloadUrl: '/downloads/manajemen-stres-pelajar.pdf',
    tags: ['stres', 'manajemen', 'pelajar', 'teknik'],
    preview: 'Modul ini membahas berbagai teknik relaksasi...'
  },
  {
    id: '3',
    title: 'Worksheet Evaluasi Diri Harian',
    description: 'Lembar kerja untuk evaluasi diri harian, membantu santri memantau perkembangan mental dan spiritual.',
    author: 'Tim SantriMental',
    category: 'Worksheet',
    fileSize: '0.5 MB',
    downloadCount: '2.1K',
    rating: 4.9,
    uploadDate: '2024-02-10',
    thumbnail: 'https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=300&h=400&fit=crop',
    downloadUrl: '/downloads/worksheet-evaluasi-diri.pdf',
    tags: ['evaluasi', 'worksheet', 'harian', 'monitoring'],
    preview: 'Lembar evaluasi untuk memantau mood harian...'
  },
  {
    id: '4',
    title: 'Kumpulan Doa untuk Ketenangan Jiwa',
    description: 'Koleksi doa-doa pilihan dari Al-Quran dan Hadits untuk menenangkan jiwa dan mengatasi kecemasan.',
    author: 'Ustadz Muhammad Ridwan',
    category: 'Spiritual',
    fileSize: '1.2 MB',
    downloadCount: '3.5K',
    rating: 5.0,
    uploadDate: '2024-01-28',
    thumbnail: 'https://images.unsplash.com/photo-1542816417-0983c9c9ad53?w=300&h=400&fit=crop',
    downloadUrl: '/downloads/doa-ketenangan-jiwa.pdf',
    tags: ['doa', 'spiritual', 'ketenangan', 'islami'],
    preview: 'Doa untuk mengatasi kecemasan dan kegelisahan...'
  },
  {
    id: '5',
    title: 'Jurnal Refleksi Mingguan Santri',
    description: 'Template jurnal untuk refleksi mingguan, membantu santri dalam introspeksi dan pengembangan diri.',
    author: 'Dr. Fatimah Zahra, M.A',
    category: 'Template',
    fileSize: '0.8 MB',
    downloadCount: '1.8K',
    rating: 4.7,
    uploadDate: '2024-02-15',
    thumbnail: 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=400&fit=crop',
    downloadUrl: '/downloads/jurnal-refleksi-mingguan.pdf',
    tags: ['jurnal', 'refleksi', 'mingguan', 'introspeksi'],
    preview: 'Template jurnal untuk refleksi dan evaluasi...'
  },
  {
    id: '6',
    title: 'Panduan Komunikasi Efektif di Pesantren',
    description: 'Buku panduan tentang cara berkomunikasi yang baik dan efektif dalam lingkungan pesantren.',
    author: 'Dr. Hasan Basri, M.Kom',
    category: 'Panduan',
    fileSize: '2.1 MB',
    downloadCount: '967',
    rating: 4.5,
    uploadDate: '2024-01-20',
    thumbnail: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=300&h=400&fit=crop',
    downloadUrl: '/downloads/komunikasi-efektif-pesantren.pdf',
    tags: ['komunikasi', 'efektif', 'pesantren', 'sosial'],
    preview: 'Bab 1: Dasar-dasar Komunikasi dalam Islam...'
  }
];

const categories = ['Semua', 'Panduan', 'Modul', 'Worksheet', 'Spiritual', 'Template'];

export default function Download() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Semua');
  const [previewMaterial, setPreviewMaterial] = useState<Material | null>(null);

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         material.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'Semua' || material.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleDownload = (material: Material) => {
    toast({
      title: 'Download Dimulai',
      description: `Mengunduh "${material.title}"...`
    });
    // Simulate download
    console.log('Downloading:', material.downloadUrl);
  };

  const handlePreview = (material: Material) => {
    setPreviewMaterial(material);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-4">
            📚 Download Materi Edukasi
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Koleksi materi pendukung berupa PDF, worksheet, dan panduan untuk kesehatan mental santri
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 space-y-4">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Cari materi edukasi..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Preview Modal */}
        {previewMaterial && (
          <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
            <div className="bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-bold">{previewMaterial.title}</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setPreviewMaterial(null)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    ✕
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {previewMaterial.author}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(previewMaterial.uploadDate).toLocaleDateString('id-ID')}
                    </span>
                    <span className="flex items-center gap-1">
                      <FileText className="w-4 h-4" />
                      {previewMaterial.fileSize}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="flex">{renderStars(previewMaterial.rating)}</div>
                    <span className="text-sm text-muted-foreground">
                      ({previewMaterial.rating}/5.0)
                    </span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Badge variant="secondary">{previewMaterial.category}</Badge>
                    {previewMaterial.tags.map((tag) => (
                      <Badge key={tag} variant="outline">#{tag}</Badge>
                    ))}
                  </div>
                  
                  <p className="text-muted-foreground">{previewMaterial.description}</p>
                  
                  {previewMaterial.preview && (
                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="font-semibold mb-2">Preview:</h4>
                      <p className="text-sm text-muted-foreground">{previewMaterial.preview}</p>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={() => handleDownload(previewMaterial)}
                      className="gap-2"
                    >
                      <DownloadIcon className="w-4 h-4" />
                      Download ({previewMaterial.fileSize})
                    </Button>
                    <Button variant="outline" size="sm" className="gap-2">
                      <Heart className="w-4 h-4" />
                      Favorit
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Materials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMaterials.map((material) => (
            <Card key={material.id} className="group hover:shadow-lg transition-all duration-300">
              <div className="relative overflow-hidden rounded-t-lg">
                <img
                  src={material.thumbnail}
                  alt={material.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {material.fileSize}
                </div>
              </div>
              
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary" className="text-xs">{material.category}</Badge>
                  <div className="flex items-center gap-1">
                    <div className="flex">{renderStars(material.rating)}</div>
                    <span className="text-xs text-muted-foreground ml-1">
                      ({material.rating})
                    </span>
                  </div>
                </div>
                <CardTitle className="text-lg line-clamp-2 group-hover:text-primary transition-colors">
                  {material.title}
                </CardTitle>
                <div className="text-sm text-muted-foreground">
                  oleh {material.author}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <CardDescription className="line-clamp-2 mb-4">
                  {material.description}
                </CardDescription>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <span className="flex items-center gap-1">
                    <Eye className="w-4 h-4" />
                    {material.downloadCount} downloads
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {new Date(material.uploadDate).toLocaleDateString('id-ID')}
                  </span>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleDownload(material)}
                    size="sm"
                    className="flex-1 gap-2"
                  >
                    <DownloadIcon className="w-4 h-4" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreview(material)}
                    className="gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    Preview
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredMaterials.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">Tidak ada materi ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah kata kunci pencarian atau pilih kategori lain
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
