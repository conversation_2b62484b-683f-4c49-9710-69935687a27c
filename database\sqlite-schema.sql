-- SantriMental Database Schema - SQLite Version
-- Compatible with SQLite 3.35+
-- Version: 2.0
-- Date: 2024

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'student' CHECK (role IN ('student', 'counselor', 'admin')),
    email_verified INTEGER DEFAULT 0,
    last_login DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- User profiles with pesantren-specific information
CREATE TABLE IF NOT EXISTS profiles (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    nama_lengkap TEXT NOT NULL,
    nomor_induk TEXT,
    jenis_kelamin TEXT CHECK (jenis_kelamin IN ('laki-laki', 'perempuan')),
    tanggal_lahir DATE NULL,
    umur INTEGER NULL,
    kelas TEXT NULL,
    tingkat_pendidikan TEXT CHECK (tingkat_pendidikan IN ('ibtidaiyah', 'tsanawiyah', 'aliyah', 'mahasiswa', 'lainnya')),
    pondok_pesantren TEXT NULL,
    alamat_pesantren TEXT NULL,
    provinsi TEXT NULL,
    kota_kabupaten TEXT NULL,
    tahun_masuk INTEGER NULL,
    status_santri TEXT DEFAULT 'aktif' CHECK (status_santri IN ('aktif', 'alumni', 'pindah', 'keluar')),
    program_studi TEXT NULL,
    wali_santri TEXT NULL,
    nomor_wali TEXT NULL,
    riwayat_konseling INTEGER DEFAULT 0,
    persetujuan_data INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_pesantren ON profiles(pondok_pesantren);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status_santri);

-- Assessment configurations and metadata
CREATE TABLE IF NOT EXISTS assessment_configs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    assessment_code TEXT NOT NULL UNIQUE,
    assessment_name TEXT NOT NULL,
    description TEXT,
    version TEXT DEFAULT '1.0',
    total_items INTEGER NOT NULL,
    estimated_time_minutes INTEGER DEFAULT 15,
    is_active INTEGER DEFAULT 1,
    requires_supervision INTEGER DEFAULT 0,
    age_min INTEGER DEFAULT 12,
    age_max INTEGER DEFAULT 30,
    cultural_context TEXT DEFAULT 'pesantren' CHECK (cultural_context IN ('general', 'islamic', 'indonesian', 'pesantren')),
    scoring_method TEXT NOT NULL CHECK (scoring_method IN ('likert', 'boolean', 'mixed')),
    clinical_cutoffs TEXT, -- JSON string
    psychometric_properties TEXT, -- JSON string
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_assessment_code ON assessment_configs(assessment_code);
CREATE INDEX IF NOT EXISTS idx_assessment_active ON assessment_configs(is_active);

-- Assessment sessions (each attempt)
CREATE TABLE IF NOT EXISTS assessment_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    assessment_code TEXT NOT NULL,
    session_status TEXT DEFAULT 'started' CHECK (session_status IN ('started', 'in_progress', 'completed', 'abandoned', 'invalid')),
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME NULL,
    duration_seconds INTEGER NULL,
    ip_address TEXT,
    user_agent TEXT,
    device_type TEXT CHECK (device_type IN ('mobile', 'tablet', 'desktop')),
    is_supervised INTEGER DEFAULT 0,
    supervisor_id TEXT NULL,
    notes TEXT,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_code) REFERENCES assessment_configs(assessment_code),
    FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL
);

CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON assessment_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_assessment ON assessment_sessions(assessment_code);
CREATE INDEX IF NOT EXISTS idx_sessions_status ON assessment_sessions(session_status);
CREATE INDEX IF NOT EXISTS idx_sessions_completed ON assessment_sessions(completed_at);

-- Individual question responses
CREATE TABLE IF NOT EXISTS assessment_responses (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    session_id TEXT NOT NULL,
    question_id TEXT NOT NULL,
    question_text TEXT NOT NULL,
    response_value INTEGER NULL,
    response_boolean INTEGER NULL,
    response_text TEXT NULL,
    response_time_seconds REAL NULL,
    is_reverse_scored INTEGER DEFAULT 0,
    domain TEXT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES assessment_sessions(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_responses_session ON assessment_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_responses_question ON assessment_responses(question_id);
CREATE INDEX IF NOT EXISTS idx_responses_domain ON assessment_responses(domain);

-- Assessment results and interpretations
CREATE TABLE IF NOT EXISTS assessment_results (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    session_id TEXT NOT NULL,
    assessment_code TEXT NOT NULL,
    user_id TEXT NOT NULL,
    
    total_raw_score REAL NULL,
    domain_scores TEXT, -- JSON string
    
    total_t_score REAL NULL,
    total_percentile REAL NULL,
    domain_t_scores TEXT, -- JSON string
    domain_percentiles TEXT, -- JSON string
    
    overall_severity TEXT CHECK (overall_severity IN ('normal', 'mild', 'moderate', 'severe', 'extremely_severe')),
    domain_severities TEXT, -- JSON string
    risk_level TEXT DEFAULT 'low' CHECK (risk_level IN ('low', 'moderate', 'high', 'crisis')),
    
    interpretation_summary TEXT,
    clinical_recommendations TEXT, -- JSON string
    referral_recommended INTEGER DEFAULT 0,
    follow_up_recommended INTEGER DEFAULT 0,
    follow_up_timeframe TEXT NULL,
    
    reliability_alpha REAL NULL,
    response_consistency REAL NULL,
    completion_percentage REAL DEFAULT 100.0,
    validity_flags TEXT, -- JSON string
    
    religious_coping_indicators TEXT, -- JSON string
    cultural_considerations TEXT,
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_code) REFERENCES assessment_configs(assessment_code)
);

CREATE INDEX IF NOT EXISTS idx_results_session ON assessment_results(session_id);
CREATE INDEX IF NOT EXISTS idx_results_user ON assessment_results(user_id);
CREATE INDEX IF NOT EXISTS idx_results_assessment ON assessment_results(assessment_code);
CREATE INDEX IF NOT EXISTS idx_results_risk ON assessment_results(risk_level);
CREATE INDEX IF NOT EXISTS idx_results_severity ON assessment_results(overall_severity);
CREATE INDEX IF NOT EXISTS idx_results_date ON assessment_results(created_at);

-- User progress tracking
CREATE TABLE IF NOT EXISTS user_progress (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    measurement_date DATE NOT NULL,
    assessment_session_id TEXT NULL,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_session_id) REFERENCES assessment_sessions(id) ON DELETE SET NULL
);

CREATE INDEX IF NOT EXISTS idx_progress_user ON user_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_progress_metric ON user_progress(metric_name);
CREATE INDEX IF NOT EXISTS idx_progress_date ON user_progress(measurement_date);

-- Crisis alerts and notifications
CREATE TABLE IF NOT EXISTS crisis_alerts (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    assessment_result_id TEXT NOT NULL,
    alert_level TEXT NOT NULL CHECK (alert_level IN ('moderate', 'high', 'crisis', 'emergency')),
    trigger_criteria TEXT, -- JSON string
    alert_message TEXT NOT NULL,
    is_acknowledged INTEGER DEFAULT 0,
    acknowledged_by TEXT NULL,
    acknowledged_at DATETIME NULL,
    action_taken TEXT,
    resolution_status TEXT DEFAULT 'pending' CHECK (resolution_status IN ('pending', 'in_progress', 'resolved', 'escalated')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_result_id) REFERENCES assessment_results(id) ON DELETE CASCADE,
    FOREIGN KEY (acknowledged_by) REFERENCES users(id) ON DELETE SET NULL
);

CREATE INDEX IF NOT EXISTS idx_alerts_user ON crisis_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_alerts_level ON crisis_alerts(alert_level);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON crisis_alerts(resolution_status);
CREATE INDEX IF NOT EXISTS idx_alerts_date ON crisis_alerts(created_at);

-- Counselor-student relationships
CREATE TABLE IF NOT EXISTS counselor_assignments (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    counselor_id TEXT NOT NULL,
    student_id TEXT NOT NULL,
    assignment_type TEXT DEFAULT 'primary' CHECK (assignment_type IN ('primary', 'secondary', 'supervisor', 'consultant')),
    assigned_date DATE NOT NULL,
    active_until DATE NULL,
    is_active INTEGER DEFAULT 1,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (counselor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE UNIQUE INDEX IF NOT EXISTS unique_primary_assignment ON counselor_assignments(student_id, assignment_type, is_active) WHERE is_active = 1;
CREATE INDEX IF NOT EXISTS idx_assignments_counselor ON counselor_assignments(counselor_id);
CREATE INDEX IF NOT EXISTS idx_assignments_student ON counselor_assignments(student_id);
CREATE INDEX IF NOT EXISTS idx_assignments_active ON counselor_assignments(is_active);

-- Educational content and progress
CREATE TABLE IF NOT EXISTS educational_modules (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    module_code TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    content_type TEXT NOT NULL CHECK (content_type IN ('video', 'article', 'interactive', 'game', 'quiz')),
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_duration_minutes INTEGER DEFAULT 10,
    prerequisites TEXT, -- JSON string
    learning_objectives TEXT, -- JSON string
    content_url TEXT,
    thumbnail_url TEXT,
    is_active INTEGER DEFAULT 1,
    islamic_context INTEGER DEFAULT 1,
    target_audience TEXT DEFAULT 'students' CHECK (target_audience IN ('students', 'counselors', 'all')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_modules_code ON educational_modules(module_code);
CREATE INDEX IF NOT EXISTS idx_modules_type ON educational_modules(content_type);
CREATE INDEX IF NOT EXISTS idx_modules_active ON educational_modules(is_active);

-- User educational progress
CREATE TABLE IF NOT EXISTS user_learning_progress (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    module_code TEXT NOT NULL,
    progress_status TEXT DEFAULT 'not_started' CHECK (progress_status IN ('not_started', 'in_progress', 'completed', 'skipped')),
    progress_percentage REAL DEFAULT 0.0,
    time_spent_minutes INTEGER DEFAULT 0,
    started_at DATETIME NULL,
    completed_at DATETIME NULL,
    quiz_scores TEXT, -- JSON string
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (module_code) REFERENCES educational_modules(module_code)
);

CREATE UNIQUE INDEX IF NOT EXISTS unique_user_module ON user_learning_progress(user_id, module_code);
CREATE INDEX IF NOT EXISTS idx_learning_user ON user_learning_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_module ON user_learning_progress(module_code);
CREATE INDEX IF NOT EXISTS idx_learning_status ON user_learning_progress(progress_status);

-- System logs and audit trail
CREATE TABLE IF NOT EXISTS audit_logs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NULL,
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT NULL,
    old_values TEXT NULL, -- JSON string
    new_values TEXT NULL, -- JSON string
    ip_address TEXT,
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_logs(timestamp);
