import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/sections/Navbar';
import { Footer } from '@/components/sections/Footer';
import { Play, Search, Clock, Eye, ThumbsUp, Share2, BookOpen, Heart, Star } from 'lucide-react';

interface Movie {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  views: string;
  likes: string;
  category: string;
  uploadDate: string;
  videoUrl: string;
  tags: string[];
}

const movies: Movie[] = [
  {
    id: '1',
    title: 'Mengelola Stres dalam Kehidupan Santri',
    description: 'Video edukasi tentang cara mengelola stres dan tekanan dalam kehidupan pesantren dengan pendekatan Islami.',
    thumbnail: 'https://images.unsplash.com/photo-1544027993-37dbfe43562a?w=400&h=225&fit=crop',
    duration: '15:30',
    views: '2.1K',
    likes: '156',
    category: 'Ke<PERSON><PERSON><PERSON>',
    uploadDate: '2 hari lalu',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    tags: ['stres', 'santri', 'islami', 'kesehatan mental']
  },
  {
    id: '2',
    title: 'Teknik Relaksasi dan Meditasi Islami',
    description: 'Panduan praktis teknik relaksasi dan meditasi berdasarkan ajaran Islam untuk ketenangan jiwa.',
    thumbnail: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=225&fit=crop',
    duration: '12:45',
    views: '1.8K',
    likes: '134',
    category: 'Spiritual',
    uploadDate: '5 hari lalu',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    tags: ['meditasi', 'relaksasi', 'spiritual', 'islami']
  },
  {
    id: '3',
    title: 'Membangun Kepercayaan Diri Santri',
    description: 'Tips dan strategi membangun kepercayaan diri untuk santri dalam menghadapi tantangan hidup.',
    thumbnail: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=225&fit=crop',
    duration: '18:20',
    views: '3.2K',
    likes: '245',
    category: 'Pengembangan Diri',
    uploadDate: '1 minggu lalu',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    tags: ['kepercayaan diri', 'motivasi', 'santri', 'pengembangan diri']
  },
  {
    id: '4',
    title: 'Mengatasi Kecemasan dengan Dzikir',
    description: 'Cara mengatasi kecemasan dan kegelisahan melalui dzikir dan doa-doa yang mustajab.',
    thumbnail: 'https://images.unsplash.com/photo-1542816417-0983c9c9ad53?w=400&h=225&fit=crop',
    duration: '10:15',
    views: '4.5K',
    likes: '312',
    category: 'Spiritual',
    uploadDate: '2 minggu lalu',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    tags: ['kecemasan', 'dzikir', 'doa', 'spiritual']
  },
  {
    id: '5',
    title: 'Komunikasi Efektif dalam Komunitas Pesantren',
    description: 'Panduan komunikasi yang baik dan efektif dalam lingkungan pesantren dan kehidupan sosial.',
    thumbnail: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=400&h=225&fit=crop',
    duration: '14:50',
    views: '1.9K',
    likes: '167',
    category: 'Sosial',
    uploadDate: '3 minggu lalu',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    tags: ['komunikasi', 'sosial', 'pesantren', 'interpersonal']
  },
  {
    id: '6',
    title: 'Manajemen Waktu untuk Santri Aktif',
    description: 'Strategi mengatur waktu antara ibadah, belajar, dan aktivitas sosial dengan efektif.',
    thumbnail: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=225&fit=crop',
    duration: '16:30',
    views: '2.7K',
    likes: '198',
    category: 'Produktivitas',
    uploadDate: '1 bulan lalu',
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    tags: ['manajemen waktu', 'produktivitas', 'santri', 'organisasi']
  }
];

const categories = ['Semua', 'Kesehatan Mental', 'Spiritual', 'Pengembangan Diri', 'Sosial', 'Produktivitas'];

export default function Movie() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Semua');
  const [selectedMovie, setSelectedMovie] = useState<Movie | null>(null);

  const filteredMovies = movies.filter(movie => {
    const matchesSearch = movie.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movie.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         movie.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'Semua' || movie.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePlayMovie = (movie: Movie) => {
    setSelectedMovie(movie);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-4">
            🎬 Film Edukasi SantriMental
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Koleksi video edukasi kesehatan mental untuk santri dengan pendekatan Islami
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 space-y-4">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Cari video edukasi..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Video Player Modal */}
        {selectedMovie && (
          <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
            <div className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-bold">{selectedMovie.title}</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedMovie(null)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    ✕
                  </Button>
                </div>
                
                <div className="aspect-video mb-4 bg-muted rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Play className="w-16 h-16 mx-auto mb-4 text-primary" />
                    <p className="text-muted-foreground">Video Player Placeholder</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      URL: {selectedMovie.videoUrl}
                    </p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {selectedMovie.views} views
                    </span>
                    <span className="flex items-center gap-1">
                      <ThumbsUp className="w-4 h-4" />
                      {selectedMovie.likes}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {selectedMovie.duration}
                    </span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Badge variant="secondary">{selectedMovie.category}</Badge>
                    {selectedMovie.tags.map((tag) => (
                      <Badge key={tag} variant="outline">#{tag}</Badge>
                    ))}
                  </div>
                  
                  <p className="text-muted-foreground">{selectedMovie.description}</p>
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="gap-2">
                      <ThumbsUp className="w-4 h-4" />
                      Suka
                    </Button>
                    <Button variant="outline" size="sm" className="gap-2">
                      <Share2 className="w-4 h-4" />
                      Bagikan
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMovies.map((movie) => (
            <Card key={movie.id} className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
              <div className="relative overflow-hidden rounded-t-lg">
                <img
                  src={movie.thumbnail}
                  alt={movie.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button
                    size="lg"
                    className="rounded-full"
                    onClick={() => handlePlayMovie(movie)}
                  >
                    <Play className="w-6 h-6 mr-2" />
                    Putar
                  </Button>
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {movie.duration}
                </div>
              </div>
              
              <CardHeader className="pb-2">
                <CardTitle className="text-lg line-clamp-2 group-hover:text-primary transition-colors">
                  {movie.title}
                </CardTitle>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="secondary" className="text-xs">{movie.category}</Badge>
                  <span>•</span>
                  <span>{movie.uploadDate}</span>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <CardDescription className="line-clamp-2 mb-3">
                  {movie.description}
                </CardDescription>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-3">
                    <span className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {movie.views}
                    </span>
                    <span className="flex items-center gap-1">
                      <ThumbsUp className="w-4 h-4" />
                      {movie.likes}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePlayMovie(movie)}
                    className="text-primary hover:text-primary/80"
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredMovies.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">Tidak ada video ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah kata kunci pencarian atau pilih kategori lain
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
