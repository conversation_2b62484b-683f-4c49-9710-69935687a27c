<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Test SantriMental API</h1>
    
    <h2>Test Registration</h2>
    <button onclick="testSignup()">Test Signup</button>
    <div id="signup-result"></div>
    
    <h2>Test Login</h2>
    <button onclick="testSignin()">Test Signin</button>
    <div id="signin-result"></div>

    <script>
        async function testSignup() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '@N300honh!'
                    })
                });
                
                const result = await response.json();
                document.getElementById('signup-result').innerHTML = 
                    `<pre>Status: ${response.status}\n${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('signup-result').innerHTML = 
                    `<pre>Error: ${error.message}</pre>`;
            }
        }

        async function testSignin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '@N300honh!'
                    })
                });
                
                const result = await response.json();
                document.getElementById('signin-result').innerHTML = 
                    `<pre>Status: ${response.status}\n${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('signin-result').innerHTML = 
                    `<pre>Error: ${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
