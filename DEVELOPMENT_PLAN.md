# Rencana Pengembangan Tokenpedia - SantriMental

## 📋 <PERSON><PERSON><PERSON> permintaan user, berikut adalah fitur-fitur yang perlu diimplementasikan:

1. **Perbaikan Navigasi**: Button login dan register harus mengarah ke halaman yang tepat
2. **Status Online/Offline**: Float indicator sejajar dengan tombol login/register dengan warna mencolok
3. **Google Authentication**: Implementasi login dengan Google
4. **Sistem Email**: Pengiriman email saat pendaftaran (<NAME_EMAIL>)
5. **Notifikasi Mobile**: Posisi notifikasi yang terlihat di mobile
6. **Icon Estetik**: Upgrade icon yang lebih bagus

## 🎯 Rencana Pengembangan

### **Phase 1: Routing & Navigation (Prioritas Tinggi)**
- [ ] 1.1 Tambah route `/login` dan `/register` di App.tsx
- [ ] 1.2 Buat halaman Register.tsx baru
- [ ] 1.3 Update tombol di Navbar untuk mengarah ke halaman yang tepat
- [ ] 1.4 Testing navigasi antar halaman

### **Phase 2: Status Online/Offline Indicator (Prioritas Tinggi)**
- [ ] 2.1 Modifikasi komponen SyncIndicator untuk posisi yang sejajar
- [ ] 2.2 Implementasi styling warna mencolok
- [ ] 2.3 Responsive design untuk mobile dan desktop
- [ ] 2.4 Testing visibility di berbagai ukuran layar

### **Phase 3: Google Authentication (Prioritas Sedang)**
- [ ] 3.1 Setup Google OAuth credentials
- [ ] 3.2 Install dan konfigurasi passport-google-oauth20
- [ ] 3.3 Implementasi backend route untuk Google auth
- [ ] 3.4 Update frontend untuk Google login button
- [ ] 3.5 Testing Google login flow

### **Phase 4: Sistem Email (Prioritas Sedang)**
- [ ] 4.1 Install dan konfigurasi nodemailer
- [ ] 4.2 Setup email templates untuk verifikasi
- [ ] 4.3 Implementasi pengiriman email di signup route
- [ ] 4.4 Testing <NAME_EMAIL>
- [ ] 4.5 Error handling untuk email delivery

### **Phase 5: Notifikasi Mobile-Friendly (Prioritas Sedang)**
- [ ] 5.1 Analisis posisi toast notifications saat ini
- [ ] 5.2 Update CSS untuk mobile visibility
- [ ] 5.3 Testing di berbagai device mobile
- [ ] 5.4 Implementasi fallback untuk browser lama

### **Phase 6: Icon Enhancement (Prioritas Rendah)**
- [ ] 6.1 Audit icon yang ada saat ini
- [ ] 6.2 Install icon library tambahan (Heroicons, Feather, dll)
- [ ] 6.3 Replace icon di komponen utama
- [ ] 6.4 Konsistensi styling icon
- [ ] 6.5 Testing visual di semua halaman

## 🔧 Technical Stack

**Frontend**:
- React 18 + TypeScript
- Vite (build tool)
- TailwindCSS + shadcn/ui
- Wouter (routing)
- TanStack Query (state management)

**Backend**:
- Express.js + TypeScript
- JWT authentication
- bcrypt (password hashing)
- Drizzle ORM
- MySQL/PostgreSQL

**Tools & Libraries**:
- Lucide React (icons)
- Sonner + shadcn/ui toast (notifications)
- Nodemailer (email - to be added)
- Passport Google OAuth (to be added)

## 📊 Progress Tracking

| Phase | Status | Progress | ETA |
|-------|--------|----------|-----|
| Phase 1: Routing & Navigation | ⏳ Pending | 0% | Prompt #2 |
| Phase 2: Status Indicator | ⏳ Pending | 0% | Prompt #3 |
| Phase 3: Google Auth | ⏳ Pending | 0% | Prompt #4 |
| Phase 4: Email System | ⏳ Pending | 0% | Prompt #5 |
| Phase 5: Mobile Notifications | ⏳ Pending | 0% | Prompt #6 |
| Phase 6: Icon Enhancement | ⏳ Pending | 0% | Prompt #7 |

**Legend**:
- ✅ Completed
- 🔄 In Progress  
- ⏳ Pending
- ❌ Blocked

## 📁 File Structure Analysis

```
tokenpedia/
├── client/src/
│   ├── App.tsx                 # Main routing (needs update)
│   ├── pages/
│   │   ├── Index.tsx          # Homepage
│   │   ├── Login.tsx          # Login page (exists)
│   │   ├── Register.tsx       # Register page (to be created)
│   │   ├── Assessments.tsx    # Assessment page
│   │   └── NotFound.tsx       # 404 page
│   ├── components/
│   │   ├── sections/
│   │   │   └── Navbar.tsx     # Navigation (needs update)
│   │   ├── SyncIndicator.tsx  # Status indicator (needs repositioning)
│   │   └── ui/                # shadcn/ui components
│   └── hooks/
│       └── use-auth.ts        # Auth hook (needs Google OAuth)
├── server/
│   ├── routes.ts              # API routes (needs email & Google auth)
│   ├── storage.ts             # Database operations
│   └── db.ts                  # Database connection
└── shared/
    └── schema.ts              # Database schema
```
