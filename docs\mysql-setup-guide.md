# 🗄️ MySQL Local Setup Guide - SantriMental

## 📋 Overview

SantriMental sudah disiapkan dengan konfigurasi MySQL local yang lengkap. Aplikasi menggunakan hybrid storage system yang mendukung MySQL untuk production dan SQLite untuk offline mode.

## ✅ Konfigurasi yang Sudah Tersedia

### 1. Database Connection Setup
```javascript
// database/setup-mysql.js
const connection = await mysql.createConnection({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root', 
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 3306,
  multipleStatements: true
});
```

### 2. Connection Pool Configuration
```javascript
// server/db.ts
export const pool = mysql.createPool({
  uri: process.env.DATABASE_URL,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});
```

### 3. Hybrid Storage System
```javascript
// server/storage.ts
export const storage = process.env.NODE_ENV === 'production' && process.env.DATABASE_URL
  ? new DatabaseStorage()
  : process.env.ENABLE_OFFLINE
    ? new OfflineStorage()
    : new MemStorage();
```

## 🚀 Quick Setup

### Step 1: Environment Configuration
Buat file `.env` di root directory:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=santrimental6
DB_PORT=3306

# Full Database URL
DATABASE_URL="mysql://root:your_password@localhost:3306/santrimental6"

# App Settings
NODE_ENV=development
PORT=5000
ENABLE_OFFLINE=true

# Security
JWT_SECRET=your_jwt_secret_key
```

### Step 2: MySQL Database Setup
```bash
# Pastikan MySQL service berjalan
sudo systemctl start mysql
# atau untuk Windows/Mac
brew services start mysql

# Jalankan setup script otomatis
node database/setup-mysql.js
```

### Step 3: Verify Connection
```bash
# Test koneksi database
mysql -u root -p -e "USE santrimental6; SHOW TABLES;"
```

## 📁 File Structure Database

```
database/
├── setup-mysql.js         # Auto setup script
├── mysql-schema.sql        # Database schema
├── schema.sql             # Alternative schema
└── migrations/            # Database migrations
```

## 🔧 Development Modes

### 1. MySQL Mode (Production)
```bash
# Set environment
NODE_ENV=production
DATABASE_URL="mysql://root:password@localhost:3306/santrimental6"

# Start application
npm run dev
```

### 2. Offline Mode (Development)
```bash
# Set environment
ENABLE_OFFLINE=true

# Start with SQLite fallback
npm run dev:offline
```

### 3. Memory Mode (Testing)
```bash
# No database required
# Data stored in memory only
npm run test
```

## 📊 Database Schema

### Core Tables
- **users**: Authentication dan user management
- **profiles**: Detail profil santri dan pesantren
- **assessment_configs**: Konfigurasi assessment tools
- **assessment_sessions**: Session assessment individual
- **assessment_results**: Hasil dan interpretasi assessment
- **crisis_alerts**: Deteksi dan alert krisis

### Assessment Tools Tables
- **dass42_responses**: DASS-42 responses
- **gse_responses**: General Self-Efficacy responses
- **mhkq_responses**: Mental Health Knowledge responses
- **mspss_responses**: Social Support responses
- **pdd_responses**: Perceived Discrimination responses
- **srq20_responses**: Self-Reporting Questionnaire responses

## 🔍 Troubleshooting

### Common Issues

#### 1. Connection Refused
```bash
# Check MySQL service
sudo systemctl status mysql

# Start MySQL if not running
sudo systemctl start mysql
```

#### 2. Access Denied
```bash
# Reset MySQL password
sudo mysql -u root -p
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

#### 3. Database Not Found
```bash
# Create database manually
mysql -u root -p -e "CREATE DATABASE santrimental6 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

#### 4. Port Already in Use
```bash
# Check what's using port 3306
sudo lsof -i :3306

# Change port in .env if needed
DB_PORT=3307
```

## 🧪 Testing Database Connection

### Manual Test
```javascript
// test-db-connection.js
const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'your_password',
      database: 'santrimental6'
    });
    
    console.log('✅ MySQL connection successful!');
    await connection.end();
  } catch (error) {
    console.error('❌ MySQL connection failed:', error.message);
  }
}

testConnection();
```

### API Test
```bash
# Test user registration
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 📈 Performance Optimization

### Connection Pool Settings
```javascript
// Recommended production settings
export const pool = mysql.createPool({
  uri: process.env.DATABASE_URL,
  waitForConnections: true,
  connectionLimit: 20,        // Increase for high traffic
  queueLimit: 0,
  acquireTimeout: 60000,      // 60 seconds
  timeout: 60000,
  reconnect: true
});
```

### Indexing Strategy
```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_profiles_user_id ON profiles(user_id);
CREATE INDEX idx_assessment_results_user_id ON assessment_results(user_id);
CREATE INDEX idx_assessment_results_created_at ON assessment_results(created_at);
```

## 🔐 Security Considerations

### 1. Environment Variables
- Jangan commit `.env` file ke repository
- Gunakan strong password untuk MySQL
- Rotate JWT secret secara berkala

### 2. Database Security
```sql
-- Create dedicated user for application
CREATE USER 'santrimental'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON santrimental6.* TO 'santrimental'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Connection Security
```javascript
// Use SSL in production
const pool = mysql.createPool({
  uri: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false
});
```

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] Database schema deployed
- [ ] Connection pool optimized
- [ ] Indexes created
- [ ] Backup strategy implemented

### Production Setup
- [ ] Dedicated MySQL user created
- [ ] SSL certificates configured
- [ ] Monitoring tools setup
- [ ] Log rotation configured
- [ ] Performance metrics tracking

## 📚 Next Steps

1. **Complete Assessment Implementation**: Implement all 6 assessment tools
2. **Add Data Validation**: Input validation dan sanitization
3. **Implement Caching**: Redis untuk session management
4. **Add Monitoring**: Database performance monitoring
5. **Setup Backups**: Automated backup strategy
6. **Load Testing**: Test dengan concurrent users

## 🔗 Related Documentation

- [Development Plan](./development-plan.md)
- [Database Schema](../database/mysql-schema.sql)
- [API Documentation](./api-documentation.md)
- [Deployment Guide](../deploy-cpanel.md)

---

**Last Updated**: 2025-01-XX  
**Version**: 1.0.0  
**Maintainer**: SantriMental Development Team