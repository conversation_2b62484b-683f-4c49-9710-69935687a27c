# 🗄️ Database Testing Guide - SantriMental

## 📋 Overview

SantriMental menggunakan sistem database multi-provider yang mendukung:
- **AIVEN MySQL** (Cloud MySQL dengan SSL)
- **Local MySQL** (MySQL lokal)
- **NEON PostgreSQL** (Cloud PostgreSQL)
- **SQLite** (Fallback database)

Testing suite ini memastikan semua database provider berfungsi dengan baik dan dapat beralih secara dinamis.

## 🚀 Quick Start

### 1. Persiapan
```bash
# Pastikan server berjalan
npm run dev

# Install dependencies (jika belum)
npm install
```

### 2. Jalankan Semua Tes Database
```bash
# Tes lengkap semua database
npm run test:db-all

# Tes cepat (konfigurasi + koneksi)
npm run test:db-quick
```

## 🧪 Test Suites Available

### 1. Configuration Testing (`test:db-config`)
```bash
npm run test:db-config
```
**Menguji:**
- ✅ Environment variables validation
- ✅ Database URL parsing
- ✅ SSL certificate validation
- ✅ Connection string format

### 2. Connection Testing (`test:db`)
```bash
npm run test:db
```
**Menguji:**
- ✅ Database status API
- ✅ Connection testing untuk semua provider
- ✅ Provider switching
- ✅ CRUD operations
- ✅ Performance basic

### 3. Migration Testing (`test:db-migration`)
```bash
npm run test:db-migration
```
**Menguji:**
- ✅ Schema files validation
- ✅ Migration API endpoints
- ✅ Backup functionality
- ✅ Data integrity
- ✅ Provider switching dengan data preservation

### 4. Performance Testing (`test:db-performance`)
```bash
npm run test:db-performance
```
**Menguji:**
- ✅ Connection speed
- ✅ Query performance
- ✅ Concurrent operations
- ✅ Load testing
- ✅ Memory usage monitoring

## 📊 Test Results

Setelah menjalankan tes, Anda akan mendapatkan:

### 1. Console Output
- Real-time progress dengan warna-warni
- Status setiap test (✅ Pass, ❌ Fail, ⚠️ Warning)
- Performance metrics
- Recommendations

### 2. JSON Reports
- `database-config-report.json` - Hasil tes konfigurasi
- `database-test-report.json` - Hasil tes koneksi
- `database-migration-report.json` - Hasil tes migrasi
- `database-performance-report.json` - Hasil tes performa
- `comprehensive-database-report.json` - Laporan lengkap

### 3. HTML Report
- `database-test-report.html` - Laporan visual yang bisa dibuka di browser

## 🔧 Environment Setup

### Required Environment Variables
```env
# Database Provider Selection (auto-detected)
DATABASE_PROVIDER="aiven-mysql"  # atau mysql, neon-postgresql, sqlite

# AIVEN MySQL (Cloud)
AIVEN_MYSQL_URL="mysql://user:pass@host:port/db?ssl-mode=REQUIRED"
AIVEN_CA_CERT_PATH="./certs/aiven-ca.pem"

# Local MySQL
DATABASE_URL="mysql://root:password@localhost:3306/santrimental6"

# NEON PostgreSQL (Cloud)
NEON_DATABASE_URL="**********************************************"

# JWT Secret (Required)
JWT_SECRET="your-256-bit-secret-key"

# Email Configuration (Optional)
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

## 📈 Understanding Test Results

### Status Indicators
- ✅ **PASSED** - Test berhasil, tidak ada masalah
- ⚠️ **WARNING** - Test berhasil tapi ada catatan/peringatan
- ❌ **FAILED** - Test gagal, perlu diperbaiki

### Performance Benchmarks
- **Connection Speed**: < 1000ms (Excellent), < 3000ms (Good)
- **Query Performance**: < 500ms (Fast), < 1500ms (Acceptable)
- **Concurrent Operations**: 100% success rate ideal
- **Load Testing**: > 95% success rate, < 1000ms avg response

## 🛠️ Troubleshooting

### Common Issues

#### 1. Server Not Running
```
❌ Server is not running at http://localhost:5000
```
**Solution:**
```bash
npm run dev
```

#### 2. Database Connection Failed
```
❌ MySQL: Connection failed - connect ECONNREFUSED
```
**Solutions:**
- Check if MySQL server is running
- Verify DATABASE_URL in .env
- Check firewall settings
- Verify credentials

#### 3. SSL Certificate Issues (AIVEN)
```
❌ AIVEN CA Certificate file not found
```
**Solutions:**
- Download certificate from AIVEN console
- Place in `./certs/aiven-ca.pem`
- Update AIVEN_CA_CERT_PATH in .env

#### 4. Environment Variables Missing
```
❌ JWT_SECRET: Missing (Required)
```
**Solution:**
```bash
# Generate secure JWT secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
# Add to .env file
```

## 🎯 Test Scenarios

### Scenario 1: Fresh Installation
```bash
# Test basic configuration
npm run test:db-config

# Test connections
npm run test:db

# If all pass, run full suite
npm run test:db-all
```

### Scenario 2: Production Readiness
```bash
# Full comprehensive testing
npm run test:db-all

# Check HTML report for detailed analysis
# Open database-test-report.html in browser
```

### Scenario 3: Performance Monitoring
```bash
# Regular performance checks
npm run test:db-performance

# Monitor trends over time
# Compare with previous reports
```

## 📝 Custom Testing

### Manual API Testing
```bash
# Test database status
curl http://localhost:5000/api/database/status

# Test all connections
curl http://localhost:5000/api/database/test-connections

# Test provider switching
curl -X POST http://localhost:5000/api/database/switch \
  -H "Content-Type: application/json" \
  -d '{"provider":"sqlite"}'
```

### Database Provider Priority
1. **AIVEN MySQL** - Jika AIVEN_MYSQL_URL tersedia
2. **NEON PostgreSQL** - Jika NEON_DATABASE_URL tersedia
3. **Local MySQL** - Jika DATABASE_URL berisi "mysql"
4. **SQLite** - Fallback (selalu tersedia)

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
name: Database Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run dev &
      - run: sleep 10  # Wait for server
      - run: npm run test:db-all
```

## 📞 Support

Jika mengalami masalah:
1. Check console output untuk error details
2. Review JSON reports untuk informasi lengkap
3. Pastikan semua environment variables sudah benar
4. Verify database servers are running
5. Check network connectivity

## 🎉 Success Criteria

Database system dianggap **READY** jika:
- ✅ Minimal 1 database provider berhasil connect
- ✅ CRUD operations berfungsi
- ✅ Performance dalam batas wajar
- ✅ No critical failures
- ✅ JWT_SECRET configured

**Total Implementation: 100% Complete! 🚀**
