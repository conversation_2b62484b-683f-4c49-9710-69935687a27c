#!/usr/bin/env node

/**
 * 🔍 Simple Endpoint Testing
 * 
 * Tests basic endpoints to ensure server is working
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:5000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function testEndpoint(url, description) {
  try {
    logInfo(`Testing ${description}...`);
    const response = await axios.get(url, { timeout: 5000 });
    
    logSuccess(`${description}: OK (${response.status})`);
    console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
    return true;
    
  } catch (error) {
    logError(`${description}: Failed`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

async function runTests() {
  log('🚀 Starting endpoint tests...', 'cyan');
  
  const tests = [
    { url: `${BASE_URL}/health`, description: 'Health Check' },
    { url: `${BASE_URL}/api/database/config`, description: 'Database Config' }
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const success = await testEndpoint(test.url, test.description);
    if (success) passed++;
    console.log(''); // Empty line for readability
  }
  
  log(`📊 Test Results: ${passed}/${total} passed`, passed === total ? 'green' : 'yellow');
  
  if (passed === total) {
    logSuccess('All tests passed! Server is working correctly.');
  } else {
    logError('Some tests failed. Please check the server.');
  }
}

runTests().catch(error => {
  logError(`Test suite failed: ${error.message}`);
  process.exit(1);
});
