import fs from 'fs';
import path from 'path';
import { ConnectionManager } from './connection-manager';

export class DatabaseMigrator {
  private connectionManager: ConnectionManager;

  constructor() {
    this.connectionManager = ConnectionManager.getInstance();
  }

  async migrateToAiven(): Promise<void> {
    console.log('🚀 Starting migration to AIVEN MySQL...');

    try {
      // Get current data from existing database
      const currentConnection = await this.connectionManager.getConnection();
      const currentData = await this.exportCurrentData(currentConnection);

      // Switch to AIVEN
      await this.connectionManager.switchProvider('aiven-mysql');
      const aivenConnection = await this.connectionManager.getConnection();

      // Create schema in AIVEN
      await this.createSchema(aivenConnection);

      // Import data to AIVEN
      await this.importData(aivenConnection, currentData);

      console.log('✅ Migration to AIVEN MySQL completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  private async exportCurrentData(connection: any): Promise<any> {
    console.log('📤 Exporting current data...');
    
    const data: any = {};
    
    try {
      // Export users
      const users = await connection.db.query.users.findMany();
      data.users = users;
      console.log(`📊 Exported ${users.length} users`);

      // Add other tables as needed
      // const profiles = await connection.db.query.profiles.findMany();
      // data.profiles = profiles;

      return data;
    } catch (error) {
      console.error('❌ Export failed:', error);
      return {};
    }
  }

  private async createSchema(connection: any): Promise<void> {
    console.log('🏗️ Creating schema in AIVEN MySQL...');
    
    const schemaPath = path.join(process.cwd(), 'database', 'mysql-schema.sql');
    
    if (fs.existsSync(schemaPath)) {
      const schema = fs.readFileSync(schemaPath, 'utf8');
      const statements = schema
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        try {
          await connection.getPool().execute(statement);
        } catch (error) {
          console.warn('⚠️ Schema statement warning:', error);
        }
      }
    }
  }

  private async importData(connection: any, data: any): Promise<void> {
    console.log('📥 Importing data to AIVEN MySQL...');

    try {
      // Import users
      if (data.users && data.users.length > 0) {
        for (const user of data.users) {
          await connection.db.insert(schema.users).values(user).ignore();
        }
        console.log(`✅ Imported ${data.users.length} users`);
      }

      // Import other tables as needed
    } catch (error) {
      console.error('❌ Import failed:', error);
      throw error;
    }
  }
}