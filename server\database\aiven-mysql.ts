import mysql from 'mysql2/promise';
import { drizzle } from 'drizzle-orm/mysql2';
import * as schema from "@shared/schema";
import { DatabaseConfig } from '../config/database';
import fs from 'fs';
import path from 'path';

export class AivenMySQLDatabase {
  private pool: mysql.Pool;
  public db: ReturnType<typeof drizzle>;

  constructor(config: DatabaseConfig) {
    console.log('🔗 Initializing AIVEN MySQL connection...');

    // Prepare SSL configuration
    let sslConfig: mysql.SslOptions | undefined = undefined;

    if (config.ssl) {
      if (typeof config.ssl === 'object') {
        sslConfig = { ...config.ssl } as mysql.SslOptions;

        // Load certificate from file if path is provided
        if (config.caCertPath && fs.existsSync(config.caCertPath)) {
          const caCert = fs.readFileSync(config.caCertPath, 'utf8');
          sslConfig.ca = caCert;
          console.log(`📜 Loaded CA certificate from: ${config.caCertPath}`);
        }
      } else if (config.ssl === true) {
        sslConfig = { rejectUnauthorized: true };
      }
    }

    // Parse connection URL to get individual components
    const url = new URL(config.url);

    this.pool = mysql.createPool({
      host: url.hostname,
      port: parseInt(url.port) || 3306,
      user: url.username,
      password: url.password,
      database: url.pathname.slice(1), // Remove leading slash
      ssl: sslConfig,
      waitForConnections: true,
      connectionLimit: config.poolConfig?.connectionLimit || 20,
      queueLimit: config.poolConfig?.queueLimit || 0
    });

    this.db = drizzle(this.pool, { schema, mode: 'default' });
  }

  async testConnection(): Promise<boolean> {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      console.log('✅ AIVEN MySQL connection successful');
      return true;
    } catch (error) {
      console.error('❌ AIVEN MySQL connection failed:', error);
      return false;
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
    console.log('🔌 AIVEN MySQL connection closed');
  }

  getPool(): mysql.Pool {
    return this.pool;
  }
}