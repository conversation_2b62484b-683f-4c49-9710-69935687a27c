import { Router } from 'express';
import { connectionManager } from '../database/connection-manager';
import { healthMonitor } from '../database/health-monitor';
import { DatabaseProvider } from '../config/database';

const router = Router();

// Get current database status
router.get('/status', async (req, res) => {
  try {
    const status = connectionManager.getHealthStatus();
    const healthReport = healthMonitor.getHealthReport();
    
    res.json({
      success: true,
      data: {
        ...status,
        health: healthReport
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test all available database connections
router.get('/test-connections', async (req, res) => {
  try {
    // This would test all configured providers
    const results = {
      mysql: false,
      'aiven-mysql': false,
      'neon-postgresql': false,
      sqlite: false
    };

    // Test current connection
    const currentProvider = connectionManager.getCurrentProvider();
    if (currentProvider) {
      const connection = await connectionManager.getConnection();
      if (connection.testConnection) {
        results[currentProvider] = await connection.testConnection();
      } else {
        results[currentProvider] = !!connection.db;
      }
    }

    res.json({
      success: true,
      data: {
        currentProvider,
        results,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Switch database provider
router.post('/switch-provider', async (req, res) => {
  try {
    const { provider } = req.body;
    
    if (!provider) {
      return res.status(400).json({
        success: false,
        error: 'Provider is required'
      });
    }

    const validProviders: DatabaseProvider[] = ['mysql', 'aiven-mysql', 'neon-postgresql', 'sqlite'];
    
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        success: false,
        error: `Invalid provider. Valid options: ${validProviders.join(', ')}`
      });
    }

    await connectionManager.switchProvider(provider);
    
    res.json({
      success: true,
      message: `Successfully switched to ${provider}`,
      data: {
        provider,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get health report
router.get('/health', async (req, res) => {
  try {
    const report = healthMonitor.getHealthReport();
    
    res.json({
      success: true,
      data: report
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Trigger manual health check
router.post('/health-check', async (req, res) => {
  try {
    await healthMonitor.performHealthCheck();
    const report = healthMonitor.getHealthReport();
    
    res.json({
      success: true,
      message: 'Health check completed',
      data: report
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start/stop health monitoring
router.post('/monitoring/:action', async (req, res) => {
  try {
    const { action } = req.params;
    const { interval } = req.body;
    
    if (action === 'start') {
      const monitoringInterval = interval || 30000; // Default 30 seconds
      healthMonitor.startMonitoring(monitoringInterval);
      
      res.json({
        success: true,
        message: `Health monitoring started with ${monitoringInterval}ms interval`
      });
    } else if (action === 'stop') {
      healthMonitor.stopMonitoring();
      
      res.json({
        success: true,
        message: 'Health monitoring stopped'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Invalid action. Use "start" or "stop"'
      });
    }
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Database metrics endpoint
router.get('/metrics', async (req, res) => {
  try {
    const status = connectionManager.getHealthStatus();
    const healthReport = healthMonitor.getHealthReport();
    const latestCheck = healthMonitor.getLatestCheck();
    
    // Prometheus-style metrics
    const metrics = [
      `# HELP database_connection_status Current database connection status`,
      `# TYPE database_connection_status gauge`,
      `database_connection_status{provider="${status.currentProvider}"} ${status.currentProvider ? 1 : 0}`,
      '',
      `# HELP database_health_status Database health status (1=healthy, 0=unhealthy)`,
      `# TYPE database_health_status gauge`,
      `database_health_status{provider="${status.currentProvider}"} ${healthReport.overall === 'healthy' ? 1 : 0}`,
      '',
      `# HELP database_response_time_ms Database response time in milliseconds`,
      `# TYPE database_response_time_ms gauge`,
      `database_response_time_ms{provider="${status.currentProvider}"} ${latestCheck?.responseTime || -1}`,
      '',
      `# HELP database_uptime_seconds Database uptime in seconds`,
      `# TYPE database_uptime_seconds counter`,
      `database_uptime_seconds ${Math.floor(healthReport.uptime / 1000)}`,
    ].join('\n');

    res.set('Content-Type', 'text/plain');
    res.send(metrics);
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Clear health history
router.delete('/health-history', async (req, res) => {
  try {
    healthMonitor.clearHistory();
    
    res.json({
      success: true,
      message: 'Health check history cleared'
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
