import Navbar from "@/components/sections/Navbar";
import Hero from "@/components/sections/Hero";
import Features from "@/components/sections/Features";
import Assessments from "@/components/sections/Assessments";
import Education from "@/components/sections/Education";
import Footer from "@/components/sections/Footer";

const Index = () => {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "SantriMental — TOKEN PEDIA",
    url: "https://465518c4-3ad6-49af-bae7-ee447a2f93f7.lovableproject.com/",
    description:
      "Aplikasi SantriMental: skrining, terapi, dan edukasi kesehatan jiwa santri.",
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <Navbar />
      <main>
        <Hero />
        <Features />
        {/* <Assessments /> intentionally moved to dedicated page */}
        <Education />
      </main>
      <Footer />
    </div>
  );
};

export default Index;

