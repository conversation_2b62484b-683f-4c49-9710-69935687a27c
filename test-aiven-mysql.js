#!/usr/bin/env node

/**
 * 🔍 AIVEN MySQL Connection Test with node-fetch
 * 
 * Tests AIVEN MySQL connection using node-fetch
 */

import fetch from 'node-fetch';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔍 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

const testConnection = async () => {
  logHeader('AIVEN MySQL Connection Test');
  
  console.log('Testing Aiven MySQL connection...');
  
  try {
    logInfo('Sending request to test-connections endpoint...');
    
    const response = await fetch('http://localhost:5000/api/database/test-connections', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000 // 30 second timeout
    });
    
    logInfo(`Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    
    console.log('\n📊 Test Result:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success) {
      logSuccess('API call successful!');
      
      // Check AIVEN MySQL specifically
      if (result.data && result.data.results) {
        const aivenResult = result.data.results['aiven-mysql'];
        const currentProvider = result.data.currentProvider;
        
        logInfo(`Current Provider: ${currentProvider}`);
        
        if (aivenResult === true) {
          logSuccess('✅ AIVEN MySQL connection successful!');
          
          // Additional details
          if (currentProvider === 'aiven-mysql') {
            logSuccess('🎯 AIVEN MySQL is currently the active provider!');
          } else {
            logInfo(`Note: Current active provider is ${currentProvider}, but AIVEN MySQL is available`);
          }
          
        } else if (aivenResult === false) {
          logError('❌ AIVEN MySQL connection failed.');
          logWarning('This could be due to:');
          logWarning('  - Network connectivity issues');
          logWarning('  - Invalid credentials');
          logWarning('  - SSL certificate problems');
          logWarning('  - Database server unavailable');
          
        } else {
          logWarning('⚠️  AIVEN MySQL result is undefined - may not be configured');
        }
        
        // Show all provider results
        console.log('\n📋 All Provider Results:');
        Object.entries(result.data.results).forEach(([provider, status]) => {
          if (status === true) {
            logSuccess(`  ${provider}: Connected ✅`);
          } else if (status === false) {
            logError(`  ${provider}: Failed ❌`);
          } else {
            logWarning(`  ${provider}: Unknown ⚠️`);
          }
        });
        
      } else {
        logWarning('⚠️  No connection results found in response');
      }
      
    } else {
      logError('❌ API call failed');
      if (result.error) {
        logError(`Error details: ${result.error}`);
      }
    }
    
  } catch (error) {
    logError('❌ An error occurred while testing the connection:');
    
    if (error.code === 'ECONNREFUSED') {
      logError('Connection refused - Server is not running');
      logInfo('Please start the server with: npm run dev');
    } else if (error.code === 'ETIMEDOUT') {
      logError('Connection timeout - Server is not responding');
    } else if (error.name === 'AbortError') {
      logError('Request timeout - Server took too long to respond');
    } else {
      logError(`Error: ${error.message}`);
      if (error.stack) {
        console.log('\n🔍 Stack trace:');
        console.log(error.stack);
      }
    }
  }
};

// Additional test functions
const testServerHealth = async () => {
  logHeader('Server Health Check');
  
  try {
    logInfo('Checking server health...');
    
    const response = await fetch('http://localhost:5000/health', {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      const health = await response.json();
      logSuccess('Server is healthy!');
      console.log('Health data:', JSON.stringify(health, null, 2));
      return true;
    } else {
      logError(`Server health check failed: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logError(`Server health check error: ${error.message}`);
    return false;
  }
};

const testDatabaseStatus = async () => {
  logHeader('Database Status Check');
  
  try {
    logInfo('Checking database status...');
    
    const response = await fetch('http://localhost:5000/api/database/status', {
      method: 'GET',
      timeout: 10000
    });
    
    if (response.ok) {
      const status = await response.json();
      logSuccess('Database status retrieved!');
      console.log('Status data:', JSON.stringify(status, null, 2));
      
      if (status.data && status.data.currentProvider) {
        logInfo(`Current database provider: ${status.data.currentProvider}`);
      }
      
      return true;
    } else {
      logWarning(`Database status check returned: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logWarning(`Database status check error: ${error.message}`);
    logInfo('This is normal if the full database system is not initialized');
    return false;
  }
};

const testProviderSwitch = async () => {
  logHeader('Provider Switch Test');
  
  try {
    logInfo('Testing switch to AIVEN MySQL...');
    
    const response = await fetch('http://localhost:5000/api/database/switch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        provider: 'aiven-mysql'
      }),
      timeout: 15000
    });
    
    if (response.ok) {
      const result = await response.json();
      
      if (result.success) {
        logSuccess('Successfully switched to AIVEN MySQL!');
        console.log('Switch result:', JSON.stringify(result, null, 2));
        return true;
      } else {
        logError(`Switch failed: ${result.error}`);
        return false;
      }
    } else {
      logWarning(`Provider switch returned: ${response.status}`);
      return false;
    }
    
  } catch (error) {
    logWarning(`Provider switch error: ${error.message}`);
    logInfo('This is normal if the provider switching API is not implemented');
    return false;
  }
};

// Main test runner
const runAllTests = async () => {
  logHeader('AIVEN MySQL Comprehensive Test Suite');
  
  console.log('🚀 Starting comprehensive AIVEN MySQL testing...\n');
  
  // Test 1: Server Health
  const serverHealthy = await testServerHealth();
  
  if (!serverHealthy) {
    logError('❌ Server is not healthy - stopping tests');
    process.exit(1);
  }
  
  // Test 2: Database Status
  await testDatabaseStatus();
  
  // Test 3: Provider Switch (optional)
  await testProviderSwitch();
  
  // Test 4: Main Connection Test
  await testConnection();
  
  // Summary
  logHeader('Test Summary');
  logSuccess('🎉 All AIVEN MySQL tests completed!');
  logInfo('Check the results above for detailed information');
};

// Run the tests
runAllTests().catch(error => {
  logError(`Test suite failed: ${error.message}`);
  process.exit(1);
});
