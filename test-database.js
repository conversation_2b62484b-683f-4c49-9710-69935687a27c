#!/usr/bin/env node

/**
 * 🗄️ Database Testing Script - SantriMental
 * 
 * Tests all database providers:
 * - AIVEN MySQL (Cloud)
 * - Local MySQL
 * - NEON PostgreSQL (Cloud)
 * - SQLite (Fallback)
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const BASE_URL = 'http://localhost:5000';
const API_BASE = `${BASE_URL}/api`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🗄️  ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(40));
  log(`📋 ${message}`, 'yellow');
  console.log('-'.repeat(40));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results storage
const testResults = {
  providers: {},
  connections: {},
  apis: {},
  migrations: {},
  performance: {},
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  }
};

function updateTestResult(category, test, status, details = '') {
  if (!testResults[category]) {
    testResults[category] = {};
  }
  
  testResults[category][test] = {
    status,
    details,
    timestamp: new Date().toISOString()
  };
  
  testResults.summary.total++;
  if (status === 'passed') {
    testResults.summary.passed++;
  } else if (status === 'failed') {
    testResults.summary.failed++;
  } else if (status === 'warning') {
    testResults.summary.warnings++;
  }
}

// Check if server is running
async function checkServerStatus() {
  logSubHeader('Server Status Check');
  
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    logSuccess(`Server is running at ${BASE_URL}`);
    logInfo(`Response: ${JSON.stringify(response.data, null, 2)}`);
    updateTestResult('server', 'status', 'passed', 'Server is running');
    return true;
  } catch (error) {
    logError(`Server is not running at ${BASE_URL}`);
    logError(`Error: ${error.message}`);
    updateTestResult('server', 'status', 'failed', error.message);
    return false;
  }
}

// Test database status endpoint
async function testDatabaseStatus() {
  logSubHeader('Database Status API');
  
  try {
    const response = await axios.get(`${API_BASE}/database/status`);
    const data = response.data;
    
    logSuccess('Database status endpoint working');
    logInfo(`Current Provider: ${data.data?.currentProvider || 'Unknown'}`);
    logInfo(`Response: ${JSON.stringify(data, null, 2)}`);
    
    updateTestResult('apis', 'status', 'passed', `Provider: ${data.data?.currentProvider}`);
    return data.data;
  } catch (error) {
    logError(`Database status API failed: ${error.message}`);
    updateTestResult('apis', 'status', 'failed', error.message);
    return null;
  }
}

// Test all database connections
async function testAllConnections() {
  logSubHeader('Database Connections Test');
  
  try {
    const response = await axios.get(`${API_BASE}/database/test-connections`);
    const data = response.data;
    
    if (data.success) {
      logSuccess('Connection test endpoint working');
      
      const results = data.data.results;
      const currentProvider = data.data.currentProvider;
      
      logInfo(`Current Provider: ${currentProvider}`);
      logInfo(`Test Results:`);
      
      Object.entries(results).forEach(([provider, status]) => {
        if (status) {
          logSuccess(`  ${provider}: Connected ✅`);
          updateTestResult('connections', provider, 'passed', 'Connection successful');
        } else {
          logWarning(`  ${provider}: Not connected ⚠️`);
          updateTestResult('connections', provider, 'warning', 'Connection failed');
        }
      });
      
      return results;
    } else {
      logError(`Connection test failed: ${data.error}`);
      updateTestResult('connections', 'test-all', 'failed', data.error);
      return null;
    }
  } catch (error) {
    logError(`Connection test API failed: ${error.message}`);
    updateTestResult('connections', 'test-all', 'failed', error.message);
    return null;
  }
}

// Test database health
async function testDatabaseHealth() {
  logSubHeader('Database Health Check');
  
  try {
    const response = await axios.get(`${API_BASE}/database/health`);
    const data = response.data;
    
    logSuccess('Database health endpoint working');
    logInfo(`Health Status: ${JSON.stringify(data.data, null, 2)}`);
    
    updateTestResult('apis', 'health', 'passed', 'Health check successful');
    return data.data;
  } catch (error) {
    logError(`Database health API failed: ${error.message}`);
    updateTestResult('apis', 'health', 'failed', error.message);
    return null;
  }
}

// Test provider switching
async function testProviderSwitching() {
  logSubHeader('Provider Switching Test');
  
  const providers = ['mysql', 'aiven-mysql', 'neon-postgresql', 'sqlite'];
  
  for (const provider of providers) {
    try {
      logInfo(`Testing switch to: ${provider}`);
      
      const response = await axios.post(`${API_BASE}/database/switch`, {
        provider: provider
      });
      
      if (response.data.success) {
        logSuccess(`✅ Successfully switched to ${provider}`);
        updateTestResult('providers', `switch-${provider}`, 'passed', 'Switch successful');
        
        // Wait a bit for connection to stabilize
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Test the connection after switch
        const statusResponse = await axios.get(`${API_BASE}/database/status`);
        const currentProvider = statusResponse.data.data?.currentProvider;
        
        if (currentProvider === provider) {
          logSuccess(`✅ Provider confirmed: ${currentProvider}`);
        } else {
          logWarning(`⚠️  Expected ${provider}, got ${currentProvider}`);
        }
        
      } else {
        logWarning(`⚠️  Switch to ${provider} failed: ${response.data.error}`);
        updateTestResult('providers', `switch-${provider}`, 'warning', response.data.error);
      }
      
    } catch (error) {
      logWarning(`⚠️  Switch to ${provider} error: ${error.message}`);
      updateTestResult('providers', `switch-${provider}`, 'warning', error.message);
    }
  }
}

// Test basic CRUD operations
async function testCrudOperations() {
  logSubHeader('CRUD Operations Test');
  
  try {
    // Test user registration (CREATE)
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      password: 'testpassword123'
    };
    
    logInfo('Testing user registration (CREATE)...');
    const registerResponse = await axios.post(`${API_BASE}/auth/signup`, testUser);
    
    if (registerResponse.data.success) {
      logSuccess('✅ User registration successful');
      updateTestResult('crud', 'create', 'passed', 'User created successfully');
      
      // Test user login (READ)
      logInfo('Testing user login (READ)...');
      const loginResponse = await axios.post(`${API_BASE}/auth/signin`, testUser);
      
      if (loginResponse.data.success) {
        logSuccess('✅ User login successful');
        updateTestResult('crud', 'read', 'passed', 'User login successful');
      } else {
        logError('❌ User login failed');
        updateTestResult('crud', 'read', 'failed', 'User login failed');
      }
      
    } else {
      logError('❌ User registration failed');
      updateTestResult('crud', 'create', 'failed', 'User registration failed');
    }
    
  } catch (error) {
    logError(`CRUD operations test failed: ${error.message}`);
    updateTestResult('crud', 'operations', 'failed', error.message);
  }
}

// Performance testing
async function testPerformance() {
  logSubHeader('Performance Testing');
  
  const tests = [
    { name: 'Database Status', endpoint: '/database/status' },
    { name: 'Database Health', endpoint: '/database/health' },
    { name: 'Connection Test', endpoint: '/database/test-connections' }
  ];
  
  for (const test of tests) {
    try {
      const startTime = Date.now();
      await axios.get(`${API_BASE}${test.endpoint}`);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration < 1000) {
        logSuccess(`✅ ${test.name}: ${duration}ms (Good)`);
        updateTestResult('performance', test.name, 'passed', `${duration}ms`);
      } else if (duration < 3000) {
        logWarning(`⚠️  ${test.name}: ${duration}ms (Acceptable)`);
        updateTestResult('performance', test.name, 'warning', `${duration}ms`);
      } else {
        logError(`❌ ${test.name}: ${duration}ms (Slow)`);
        updateTestResult('performance', test.name, 'failed', `${duration}ms`);
      }
      
    } catch (error) {
      logError(`❌ ${test.name}: Failed - ${error.message}`);
      updateTestResult('performance', test.name, 'failed', error.message);
    }
  }
}

// Generate test report
function generateReport() {
  logHeader('Test Report Summary');
  
  const { total, passed, failed, warnings } = testResults.summary;
  
  logInfo(`Total Tests: ${total}`);
  logSuccess(`Passed: ${passed}`);
  logError(`Failed: ${failed}`);
  logWarning(`Warnings: ${warnings}`);
  
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
  logInfo(`Success Rate: ${successRate}%`);
  
  // Save detailed report
  const reportPath = path.join(__dirname, 'database-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  logInfo(`Detailed report saved to: ${reportPath}`);
  
  // Overall status
  if (failed === 0 && warnings === 0) {
    logSuccess('\n🎉 All tests passed! Database system is fully functional.');
  } else if (failed === 0) {
    logWarning('\n⚠️  All tests passed with some warnings. System is functional but needs attention.');
  } else {
    logError('\n❌ Some tests failed. Please check the issues above.');
  }
}

// Main test runner
async function runAllTests() {
  logHeader('Database Testing Suite - SantriMental');
  
  // Check if server is running
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    logError('❌ Server is not running. Please start the server first.');
    logInfo('Run: npm run dev');
    process.exit(1);
  }
  
  // Run all tests
  await testDatabaseStatus();
  await testAllConnections();
  await testDatabaseHealth();
  await testProviderSwitching();
  await testCrudOperations();
  await testPerformance();
  
  // Generate report
  generateReport();
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    logError(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

export {
  runAllTests,
  testResults
};
