
-- SantriMental Database Schema
-- Compatible with MySQL 5.6+
-- Version: 2.1
-- Date: 2024
--
-- MySQL 5.6 Compatibility Notes:
-- - JSON columns converted to TEXT (JSON data type introduced in MySQL 5.7)
-- - JSON functions replaced with string literals
-- - UUID() default values removed (not supported as default in MySQL)
-- - UUIDs must be generated in application code
-- - All syntax verified for MySQL 5.6 compatibility

-- Enable UUID extension for PostgreSQL (comment out for MySQL)
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for authentication
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY, -- UUID generated in application code
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('student', 'counselor', 'admin') DEFAULT 'student',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_users_email (email),
    INDEX idx_users_role (role)
);

-- User profiles with pesantren-specific information
CREATE TABLE profiles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    nama_lengkap VARCHAR(255) NOT NULL,
    nomor_induk VARCHAR(50),
    jenis_kelamin ENUM('laki-laki', 'perempuan') NULL,
    tanggal_lahir DATE NULL,
    umur INT NULL,
    kelas VARCHAR(100) NULL,
    tingkat_pendidikan ENUM('ibtidaiyah', 'tsanawiyah', 'aliyah', 'mahasiswa', 'lainnya') NULL,
    pondok_pesantren VARCHAR(255) NULL,
    alamat_pesantren TEXT NULL,
    provinsi VARCHAR(100) NULL,
    kota_kabupaten VARCHAR(100) NULL,
    tahun_masuk INT NULL,
    status_santri ENUM('aktif', 'alumni', 'pindah', 'keluar') DEFAULT 'aktif',
    program_studi VARCHAR(255) NULL,
    wali_santri VARCHAR(255) NULL,
    nomor_wali VARCHAR(20) NULL,
    riwayat_konseling BOOLEAN DEFAULT FALSE,
    persetujuan_data BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_profiles_user_id (user_id),
    INDEX idx_profiles_pesantren (pondok_pesantren),
    INDEX idx_profiles_status (status_santri)
);

-- Assessment configurations and metadata
CREATE TABLE assessment_configs (
    id VARCHAR(36) PRIMARY KEY,
    assessment_code VARCHAR(20) NOT NULL UNIQUE,
    assessment_name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(10) DEFAULT '1.0',
    total_items INT NOT NULL,
    estimated_time_minutes INT DEFAULT 15,
    is_active BOOLEAN DEFAULT TRUE,
    requires_supervision BOOLEAN DEFAULT FALSE,
    age_min INT DEFAULT 12,
    age_max INT DEFAULT 30,
    cultural_context ENUM('general', 'islamic', 'indonesian', 'pesantren') DEFAULT 'pesantren',
    scoring_method ENUM('likert', 'boolean', 'mixed') NOT NULL,
    clinical_cutoffs TEXT, -- Stores cut-off scores for interpretation (JSON format)
    psychometric_properties TEXT, -- Reliability, validity data (JSON format)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_assessment_code (assessment_code),
    INDEX idx_assessment_active (is_active)
);

-- Assessment sessions (each attempt)
CREATE TABLE assessment_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    assessment_code VARCHAR(20) NOT NULL,
    session_status ENUM('started', 'in_progress', 'completed', 'abandoned', 'invalid') DEFAULT 'started',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    duration_seconds INT NULL,
    ip_address VARCHAR(45), -- Supports IPv6
    user_agent TEXT,
    device_type ENUM('mobile', 'tablet', 'desktop') NULL,
    is_supervised BOOLEAN DEFAULT FALSE,
    supervisor_id VARCHAR(36) NULL,
    notes TEXT,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_code) REFERENCES assessment_configs(assessment_code),
    FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_sessions_user_id (user_id),
    INDEX idx_sessions_assessment (assessment_code),
    INDEX idx_sessions_status (session_status),
    INDEX idx_sessions_completed (completed_at)
);

-- Individual question responses
CREATE TABLE assessment_responses (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    question_id VARCHAR(50) NOT NULL, -- e.g., 'dass42_q1', 'gse_q5'
    question_text TEXT NOT NULL,
    response_value INT NULL, -- For Likert scales
    response_boolean BOOLEAN NULL, -- For true/false questions
    response_text TEXT NULL, -- For open-ended responses
    response_time_seconds DECIMAL(10,2) NULL, -- Time spent on this question
    is_reverse_scored BOOLEAN DEFAULT FALSE,
    domain VARCHAR(50) NULL, -- Subscale/domain (e.g., 'depression', 'anxiety')
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    
    INDEX idx_responses_session (session_id),
    INDEX idx_responses_question (question_id),
    INDEX idx_responses_domain (domain)
);

-- Assessment results and interpretations
CREATE TABLE assessment_results (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    assessment_code VARCHAR(20) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    
    -- Raw scores
    total_raw_score DECIMAL(10,2) NULL,
    domain_scores TEXT, -- JSON format: {"depression": 15, "anxiety": 12, "stress": 18}

    -- Standardized scores
    total_t_score DECIMAL(5,2) NULL,
    total_percentile DECIMAL(5,2) NULL,
    domain_t_scores TEXT, -- JSON format
    domain_percentiles TEXT, -- JSON format

    -- Clinical interpretation
    overall_severity ENUM('normal', 'mild', 'moderate', 'severe', 'extremely_severe') NULL,
    domain_severities TEXT, -- JSON format
    risk_level ENUM('low', 'moderate', 'high', 'crisis') DEFAULT 'low',

    -- Narrative interpretation
    interpretation_summary TEXT,
    clinical_recommendations TEXT, -- JSON format
    referral_recommended BOOLEAN DEFAULT FALSE,
    follow_up_recommended BOOLEAN DEFAULT FALSE,
    follow_up_timeframe VARCHAR(50) NULL,

    -- Quality indicators
    reliability_alpha DECIMAL(5,3) NULL, -- Cronbach's alpha for this session
    response_consistency DECIMAL(5,3) NULL, -- Consistency check score
    completion_percentage DECIMAL(5,2) DEFAULT 100.00,
    validity_flags TEXT, -- JSON format: {"random_responding": false, "acquiescence": false}

    -- Islamic context integration
    religious_coping_indicators TEXT, -- JSON format
    cultural_considerations TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES assessment_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_code) REFERENCES assessment_configs(assessment_code),
    
    INDEX idx_results_session (session_id),
    INDEX idx_results_user (user_id),
    INDEX idx_results_assessment (assessment_code),
    INDEX idx_results_risk (risk_level),
    INDEX idx_results_severity (overall_severity),
    INDEX idx_results_date (created_at)
);

-- User progress tracking
CREATE TABLE user_progress (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    metric_name VARCHAR(100) NOT NULL, -- 'dass42_depression', 'gse_total', etc.
    metric_value DECIMAL(10,2) NOT NULL,
    measurement_date DATE NOT NULL,
    assessment_session_id VARCHAR(36) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_session_id) REFERENCES assessment_sessions(id) ON DELETE SET NULL,
    
    INDEX idx_progress_user (user_id),
    INDEX idx_progress_metric (metric_name),
    INDEX idx_progress_date (measurement_date)
);

-- Crisis alerts and notifications
CREATE TABLE crisis_alerts (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    assessment_result_id VARCHAR(36) NOT NULL,
    alert_level ENUM('moderate', 'high', 'crisis', 'emergency') NOT NULL,
    trigger_criteria TEXT, -- What triggered the alert (JSON format)
    alert_message TEXT NOT NULL,
    is_acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by VARCHAR(36) NULL,
    acknowledged_at TIMESTAMP NULL,
    action_taken TEXT,
    resolution_status ENUM('pending', 'in_progress', 'resolved', 'escalated') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_result_id) REFERENCES assessment_results(id) ON DELETE CASCADE,
    FOREIGN KEY (acknowledged_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_alerts_user (user_id),
    INDEX idx_alerts_level (alert_level),
    INDEX idx_alerts_status (resolution_status),
    INDEX idx_alerts_date (created_at)
);

-- Counselor-student relationships
CREATE TABLE counselor_assignments (
    id VARCHAR(36) PRIMARY KEY,
    counselor_id VARCHAR(36) NOT NULL,
    student_id VARCHAR(36) NOT NULL,
    assignment_type ENUM('primary', 'secondary', 'supervisor', 'consultant') DEFAULT 'primary',
    assigned_date DATE NOT NULL,
    active_until DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (counselor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_primary_assignment (student_id, assignment_type, is_active),
    INDEX idx_assignments_counselor (counselor_id),
    INDEX idx_assignments_student (student_id),
    INDEX idx_assignments_active (is_active)
);

-- Educational content and progress
CREATE TABLE educational_modules (
    id VARCHAR(36) PRIMARY KEY,
    module_code VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content_type ENUM('video', 'article', 'interactive', 'game', 'quiz') NOT NULL,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    estimated_duration_minutes INT DEFAULT 10,
    prerequisites TEXT, -- JSON format: Array of required module codes
    learning_objectives TEXT, -- JSON format
    content_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    islamic_context BOOLEAN DEFAULT TRUE,
    target_audience ENUM('students', 'counselors', 'all') DEFAULT 'students',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_modules_code (module_code),
    INDEX idx_modules_type (content_type),
    INDEX idx_modules_active (is_active)
);

-- User educational progress
CREATE TABLE user_learning_progress (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    module_code VARCHAR(50) NOT NULL,
    progress_status ENUM('not_started', 'in_progress', 'completed', 'skipped') DEFAULT 'not_started',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    time_spent_minutes INT DEFAULT 0,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    quiz_scores TEXT, -- JSON format: If module has quizzes
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (module_code) REFERENCES educational_modules(module_code),
    
    UNIQUE KEY unique_user_module (user_id, module_code),
    INDEX idx_learning_user (user_id),
    INDEX idx_learning_module (module_code),
    INDEX idx_learning_status (progress_status)
);

-- System logs and audit trail
CREATE TABLE audit_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(36) NULL,
    old_values TEXT NULL, -- JSON format
    new_values TEXT NULL, -- JSON format
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_audit_user (user_id),
    INDEX idx_audit_action (action),
    INDEX idx_audit_resource (resource_type, resource_id),
    INDEX idx_audit_timestamp (timestamp)
);

-- Insert initial assessment configurations
INSERT INTO assessment_configs (
    id, assessment_code, assessment_name, description, total_items, estimated_time_minutes,
    scoring_method, clinical_cutoffs, psychometric_properties
) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001', 'DASS42', 'Depression Anxiety Stress Scales-42',
    'Mengukur tingkat depresi, kecemasan, dan stres berdasarkan pengalaman 7 hari terakhir',
    42, 15, 'likert',
    '{"depression":{"normal":9,"mild":13,"moderate":20,"severe":27},"anxiety":{"normal":7,"mild":9,"moderate":14,"severe":19},"stress":{"normal":14,"mild":18,"moderate":25,"severe":33}}',
    '{"reliability":0.91,"validity":"high","normative_sample":"indonesian_students"}'
),
(
    '550e8400-e29b-41d4-a716-446655440002', 'GSE', 'General Self-Efficacy Scale',
    'Mengukur keyakinan diri umum dalam menghadapi tantangan hidup',
    10, 8, 'likert',
    '{"total":{"low":25,"moderate":32,"high":40}}',
    '{"reliability":0.86,"validity":"high"}'
),
(
    '550e8400-e29b-41d4-a716-446655440003', 'MHKQ', 'Mental Health Knowledge Questionnaire',
    'Menilai tingkat pengetahuan tentang kesehatan mental dan cara penanganannya',
    20, 12, 'boolean',
    '{"total":{"low":10,"moderate":15,"high":20}}',
    '{"reliability":0.78,"validity":"moderate"}'
),
(
    '550e8400-e29b-41d4-a716-446655440004', 'MSPSS', 'Multidimensional Scale of Perceived Social Support',
    'Mengukur persepsi dukungan sosial dari keluarga, teman, dan figur penting',
    12, 10, 'likert',
    '{"total":{"low":48,"moderate":68,"high":84},"family":{"low":16,"moderate":22,"high":28},"friends":{"low":16,"moderate":22,"high":28},"significant_other":{"low":16,"moderate":22,"high":28}}',
    '{"reliability":0.88,"validity":"high"}'
),
(
    '550e8400-e29b-41d4-a716-446655440005', 'PDD', 'Perceived Devaluation-Discrimination Scale',
    'Mengukur persepsi stigma dan diskriminasi terhadap masalah kesehatan mental',
    12, 8, 'likert',
    '{"total":{"low":24,"moderate":36,"high":48}}',
    '{"reliability":0.82,"validity":"high"}'
),
(
    '550e8400-e29b-41d4-a716-446655440006', 'SRQ20', 'Self-Reporting Questionnaire-20',
    'Skrining umum gangguan mental berdasarkan standar WHO',
    20, 10, 'boolean',
    '{"total":{"normal":7,"probable_disorder":8}}',
    '{"reliability":0.85,"validity":"high","who_validated":true}'
);

-- Insert sample educational modules
INSERT INTO educational_modules (
    id, module_code, title, description, content_type, difficulty_level,
    estimated_duration_minutes, learning_objectives, islamic_context
) VALUES
(
    '550e8400-e29b-41d4-a716-446655440007', 'MH_BASICS', 'Dasar-dasar Kesehatan Mental dalam Islam',
    'Pengenalan konsep kesehatan mental dari perspektif Islam',
    'article', 'beginner', 15,
    '["Memahami definisi kesehatan mental", "Mengetahui pandangan Islam tentang kesehatan jiwa"]',
    true
),
(
    '550e8400-e29b-41d4-a716-446655440008', 'STRESS_MGMT', 'Manajemen Stres dengan Pendekatan Islami',
    'Teknik-teknik mengelola stres menggunakan ajaran Islam',
    'interactive', 'intermediate', 20,
    '["Mengenali tanda-tanda stres", "Menerapkan teknik relaksasi Islami"]',
    true
),
(
    '550e8400-e29b-41d4-a716-446655440009', 'PEER_SUPPORT', 'Dukungan Sesama Santri',
    'Cara memberikan dan mencari dukungan dari sesama santri',
    'video', 'beginner', 12,
    '["Memahami pentingnya dukungan sosial", "Teknik komunikasi empatis"]',
    true
);

-- Create triggers for updated_at columns (MySQL syntax)
-- For PostgreSQL, replace with appropriate trigger functions

DELIMITER $$

CREATE TRIGGER users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER assessment_configs_updated_at
    BEFORE UPDATE ON assessment_configs
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER assessment_results_updated_at
    BEFORE UPDATE ON assessment_results
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER crisis_alerts_updated_at
    BEFORE UPDATE ON crisis_alerts
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER counselor_assignments_updated_at
    BEFORE UPDATE ON counselor_assignments
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER educational_modules_updated_at
    BEFORE UPDATE ON educational_modules
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

CREATE TRIGGER user_learning_progress_updated_at
    BEFORE UPDATE ON user_learning_progress
    FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$

DELIMITER ;

-- Create views for common queries (MySQL 5.6 compatible - no window functions)
CREATE VIEW user_latest_assessments AS
SELECT
    u.id as user_id,
    u.email,
    p.nama_lengkap,
    p.pondok_pesantren,
    ar.assessment_code,
    ar.total_raw_score,
    ar.overall_severity,
    ar.risk_level,
    ar.created_at as assessment_date
FROM users u
JOIN profiles p ON u.id = p.user_id
JOIN assessment_results ar ON u.id = ar.user_id
JOIN (
    SELECT user_id, assessment_code, MAX(created_at) as max_date
    FROM assessment_results
    WHERE completion_percentage >= 80.0
    GROUP BY user_id, assessment_code
) latest ON ar.user_id = latest.user_id
    AND ar.assessment_code = latest.assessment_code
    AND ar.created_at = latest.max_date
WHERE ar.completion_percentage >= 80.0;

CREATE VIEW high_risk_students AS
SELECT 
    u.id as user_id,
    u.email,
    p.nama_lengkap,
    p.pondok_pesantren,
    ar.assessment_code,
    ar.risk_level,
    ar.overall_severity,
    ar.created_at as assessment_date,
    ca.counselor_id
FROM users u
JOIN profiles p ON u.id = p.user_id
JOIN assessment_results ar ON u.id = ar.user_id
LEFT JOIN counselor_assignments ca ON u.id = ca.student_id AND ca.is_active = true
WHERE ar.risk_level IN ('high', 'crisis')
AND ar.created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY);

CREATE VIEW assessment_completion_stats AS
SELECT 
    ac.assessment_code,
    ac.assessment_name,
    COUNT(DISTINCT ar.user_id) as total_completions,
    AVG(ar.completion_percentage) as avg_completion_rate,
    COUNT(CASE WHEN ar.risk_level = 'high' THEN 1 END) as high_risk_count,
    COUNT(CASE WHEN ar.risk_level = 'crisis' THEN 1 END) as crisis_count
FROM assessment_configs ac
LEFT JOIN assessment_results ar ON ac.assessment_code = ar.assessment_code
WHERE ar.created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 90 DAY)
GROUP BY ac.assessment_code, ac.assessment_name;

-- Add comments for documentation (MySQL 5.6 compatible)
ALTER TABLE users COMMENT = 'User authentication and basic information';
ALTER TABLE profiles COMMENT = 'Extended user profiles with pesantren-specific data';
ALTER TABLE assessment_configs COMMENT = 'Assessment tool configurations and metadata (JSON stored as TEXT)';
ALTER TABLE assessment_sessions COMMENT = 'Individual assessment sessions/attempts';
ALTER TABLE assessment_responses COMMENT = 'Individual question responses within sessions';
ALTER TABLE assessment_results COMMENT = 'Calculated results and clinical interpretations (JSON stored as TEXT)';
ALTER TABLE user_progress COMMENT = 'Longitudinal tracking of user metrics';
ALTER TABLE crisis_alerts COMMENT = 'Crisis detection and alert management (JSON stored as TEXT)';
ALTER TABLE counselor_assignments COMMENT = 'Counselor-student relationships and assignments';
ALTER TABLE educational_modules COMMENT = 'Educational content and learning materials (JSON stored as TEXT)';
ALTER TABLE user_learning_progress COMMENT = 'User progress through educational modules (JSON stored as TEXT)';
ALTER TABLE audit_logs COMMENT = 'System audit trail and security logging (JSON stored as TEXT)';
