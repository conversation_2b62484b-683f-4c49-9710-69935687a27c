# 🚀 AIVEN MySQL Setup Guide - SantriMental

## 📋 Quick Setup Checklist

### 1. Create AIVEN Account
- [ ] Sign up at [aiven.io](https://aiven.io)
- [ ] Verify email address
- [ ] Complete account setup

### 2. Create MySQL Service
- [ ] Go to AIVEN Console
- [ ] Click "Create Service"
- [ ] Select "MySQL"
- [ ] Choose plan (Free tier available)
- [ ] Select region (closest to your users)
- [ ] Name your service: `santrimental-mysql`

### 3. Get Connection Details
- [ ] Wait for service to be "Running" (2-5 minutes)
- [ ] Go to service overview
- [ ] Copy connection details:
  - Host
  - Port
  - Username
  - Password
  - Database name

### 4. Download CA Certificate
- [ ] In service overview, go to "Connection information"
- [ ] Download CA certificate
- [ ] Save as `aiven-ca.pem`

### 5. Configure Environment Variables

Add to your `.env` file:

```env
# AIVEN MySQL Configuration
AIVEN_MYSQL_URL="mysql://username:password@hostname:port/database?ssl-mode=REQUIRED"
AIVEN_CA_CERT="-----BEGIN CERTIFICATE-----
MIIEQTCCAqmgAwIBAgIUQaQZLQ...
...certificate content...
-----END CERTIFICATE-----"

# Set as primary provider
DATABASE_PROVIDER="aiven-mysql"
```

### 6. Test Connection

```bash
# Start the application
npm run dev

# Check database status
curl http://localhost:5000/api/database/status

# Or visit the dashboard
# http://localhost:5000/database
```

## 🔧 Detailed Configuration

### Connection String Format
```
mysql://[username]:[password]@[hostname]:[port]/[database]?ssl-mode=REQUIRED
```

### Example Configuration
```env
AIVEN_MYSQL_URL="mysql://avnadmin:<EMAIL>:12345/defaultdb?ssl-mode=REQUIRED"
```

### CA Certificate Setup
1. Copy the certificate from AIVEN console
2. Replace newlines with `\n` in the environment variable
3. Or save to file and reference the path

## 🧪 Testing Your Setup

### 1. Connection Test
```bash
curl -X GET http://localhost:5000/api/database/test-connections
```

### 2. Switch to AIVEN
```bash
curl -X POST http://localhost:5000/api/database/switch-provider \
  -H "Content-Type: application/json" \
  -d '{"provider": "aiven-mysql"}'
```

### 3. Health Check
```bash
curl -X GET http://localhost:5000/api/database/health
```

### 4. Dashboard Monitoring
Visit: `http://localhost:5000/database`

## 🔍 Troubleshooting

### Common Issues

#### SSL Certificate Error
```
Error: unable to verify the first certificate
```
**Solution**: Ensure CA certificate is properly formatted in environment variable

#### Connection Timeout
```
Error: connect ETIMEDOUT
```
**Solutions**:
- Check firewall settings
- Verify hostname and port
- Ensure service is running in AIVEN console

#### Authentication Failed
```
Error: Access denied for user
```
**Solutions**:
- Verify username and password
- Check if user has proper permissions
- Ensure database name is correct

### Debug Mode
Enable debug logging:
```env
DEBUG=true
LOG_LEVEL=debug
```

## 📊 Performance Optimization

### Connection Pool Settings
```env
# Optimize for AIVEN
AIVEN_POOL_SIZE=20
AIVEN_QUEUE_LIMIT=0
AIVEN_ACQUIRE_TIMEOUT=60000
AIVEN_TIMEOUT=60000
```

### Recommended Settings
- **Connection Limit**: 20 (for free tier)
- **SSL**: Always enabled
- **Timeout**: 60 seconds
- **Retry Logic**: Enabled

## 🔄 Migration from Local MySQL

### 1. Export Local Data
```bash
mysqldump -u root -p santrimental6 > backup.sql
```

### 2. Import to AIVEN
```bash
mysql -h hostname -P port -u username -p database < backup.sql
```

### 3. Switch Provider
```bash
curl -X POST http://localhost:5000/api/database/switch-provider \
  -H "Content-Type: application/json" \
  -d '{"provider": "aiven-mysql"}'
```

### 4. Verify Migration
- Check data integrity
- Test application functionality
- Monitor performance

## 🛡️ Security Best Practices

### Environment Variables
- Never commit `.env` files
- Use different credentials for different environments
- Rotate passwords regularly

### SSL Configuration
- Always use SSL in production
- Verify certificate validity
- Keep CA certificates updated

### Access Control
- Use least privilege principle
- Create application-specific users
- Monitor access logs

## 📈 Monitoring & Alerts

### AIVEN Console
- Monitor service health
- Check performance metrics
- Set up alerts

### Application Monitoring
- Use `/api/database/health` endpoint
- Monitor response times
- Track error rates

### Dashboard Features
- Real-time status
- Historical data
- Performance graphs

## 🚀 Production Deployment

### Environment Setup
```env
NODE_ENV=production
DATABASE_PROVIDER=aiven-mysql
AIVEN_MYSQL_URL=mysql://...
AIVEN_CA_CERT=-----BEGIN CERTIFICATE-----...
```

### Health Monitoring
```bash
# Start monitoring
curl -X POST http://localhost:5000/api/database/monitoring/start \
  -H "Content-Type: application/json" \
  -d '{"interval": 30000}'
```

### Backup Strategy
- Enable AIVEN automatic backups
- Set retention period
- Test restore procedures

## 📞 Support

### AIVEN Support
- Documentation: [docs.aiven.io](https://docs.aiven.io)
- Support tickets via console
- Community forum

### Application Support
- Check logs: `npm run logs`
- Database dashboard: `/database`
- Health endpoint: `/api/database/health`

## ✅ Success Criteria

Your AIVEN MySQL setup is successful when:
- [ ] Connection test passes
- [ ] Health check shows "healthy"
- [ ] Dashboard displays correct status
- [ ] Application functions normally
- [ ] No SSL errors in logs
- [ ] Response times < 100ms

## 🎯 Next Steps

After successful AIVEN setup:
1. Configure monitoring alerts
2. Set up automated backups
3. Implement read replicas (if needed)
4. Optimize queries for cloud performance
5. Set up staging environment

---

**🎉 Congratulations! Your SantriMental application is now running on AIVEN MySQL with full monitoring and failover capabilities!**
