-- PostgreSQL Schema for SantriMental
-- Multi-provider database support

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table with role-based access control
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) NOT NULL UNIQUE,
  password TEXT NOT NULL,
  role VARCHAR(20) DEFAULT 'user' NOT NULL,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  last_login_at TIMESTAMP,
  email_verified BOOLEAN DEFAULT FALSE NOT NULL,
  email_verified_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create indexes for users
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);

-- Profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  nama_lengkap VARCHAR(255) NOT NULL,
  nomor_induk VARCHAR(50),
  jenis_kelamin VARCHAR(10),
  tanggal_lahir VARCHAR(20),
  kelas VARCHAR(20),
  pondok_pesantren VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);

-- Assessment configurations
CREATE TABLE IF NOT EXISTS assessment_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  assessment_code VARCHAR(20) NOT NULL UNIQUE,
  assessment_name VARCHAR(255) NOT NULL,
  description TEXT,
  version VARCHAR(10) DEFAULT '1.0',
  total_items INTEGER NOT NULL,
  estimated_time_minutes INTEGER DEFAULT 15,
  is_active BOOLEAN DEFAULT TRUE,
  requires_supervision BOOLEAN DEFAULT FALSE,
  age_min INTEGER DEFAULT 12,
  age_max INTEGER DEFAULT 30,
  cultural_context VARCHAR(20) DEFAULT 'pesantren',
  scoring_method VARCHAR(20) NOT NULL,
  clinical_cutoffs TEXT,
  psychometric_properties TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_assessment_configs_code ON assessment_configs(assessment_code);
CREATE INDEX IF NOT EXISTS idx_assessment_configs_active ON assessment_configs(is_active);

-- Assessment sessions
CREATE TABLE IF NOT EXISTS assessment_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  assessment_code VARCHAR(20) NOT NULL,
  session_status VARCHAR(20) DEFAULT 'started',
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  completed_at TIMESTAMP,
  duration_seconds INTEGER,
  ip_address VARCHAR(45),
  user_agent TEXT,
  device_type VARCHAR(20),
  is_supervised BOOLEAN DEFAULT FALSE,
  supervisor_id UUID,
  notes TEXT
);

CREATE INDEX IF NOT EXISTS idx_assessment_sessions_user_id ON assessment_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_code ON assessment_sessions(assessment_code);
CREATE INDEX IF NOT EXISTS idx_assessment_sessions_status ON assessment_sessions(session_status);

-- Assessment results
CREATE TABLE IF NOT EXISTS assessment_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id UUID NOT NULL,
  assessment_code VARCHAR(20) NOT NULL,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  total_raw_score VARCHAR(10),
  domain_scores TEXT,
  total_t_score VARCHAR(10),
  total_percentile VARCHAR(10),
  domain_t_scores TEXT,
  domain_percentiles TEXT,
  overall_severity VARCHAR(20),
  domain_severities TEXT,
  risk_level VARCHAR(20) DEFAULT 'low',
  interpretation_summary TEXT,
  clinical_recommendations TEXT,
  referral_recommended BOOLEAN DEFAULT FALSE,
  follow_up_recommended BOOLEAN DEFAULT FALSE,
  follow_up_timeframe VARCHAR(50),
  reliability_alpha VARCHAR(10),
  response_consistency VARCHAR(10),
  completion_percentage VARCHAR(10) DEFAULT '100.00',
  validity_flags TEXT,
  religious_coping_indicators TEXT,
  cultural_considerations TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_assessment_results_session_id ON assessment_results(session_id);
CREATE INDEX IF NOT EXISTS idx_assessment_results_user_id ON assessment_results(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_results_code ON assessment_results(assessment_code);

-- User sessions for session management
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  session_token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at);

-- User permissions for granular access control
CREATE TABLE IF NOT EXISTS user_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  permission VARCHAR(100) NOT NULL,
  granted_by UUID REFERENCES users(id) ON DELETE SET NULL,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  UNIQUE(user_id, permission)
);

CREATE INDEX IF NOT EXISTS idx_permissions_user ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_permissions_permission ON user_permissions(permission);

-- Audit logs for tracking admin actions
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50),
  resource_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_created ON audit_logs(created_at);

-- Create default admin user
INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
VALUES (
  uuid_generate_v4(),
  '<EMAIL>',
  '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
  'admin',
  TRUE,
  TRUE,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- Insert default admin permissions
INSERT INTO user_permissions (id, user_id, permission, granted_at)
SELECT 
  uuid_generate_v4(),
  u.id,
  p.permission,
  CURRENT_TIMESTAMP
FROM users u
CROSS JOIN (
  VALUES 
    ('admin.dashboard'),
    ('admin.users.view'),
    ('admin.users.create'),
    ('admin.users.edit'),
    ('admin.users.delete'),
    ('admin.assessments.view'),
    ('admin.assessments.manage'),
    ('admin.reports.view'),
    ('admin.reports.export'),
    ('admin.system.settings'),
    ('admin.system.logs'),
    ('admin.database.manage')
) p(permission)
WHERE u.role = 'admin'
ON CONFLICT (user_id, permission) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_configs_updated_at BEFORE UPDATE ON assessment_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_results_updated_at BEFORE UPDATE ON assessment_results FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON user_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
