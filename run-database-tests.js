#!/usr/bin/env node

/**
 * 🚀 Database Testing Suite Runner
 * 
 * Runs all database tests and generates comprehensive reports
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(80));
  log(`🚀 ${message}`, 'cyan');
  console.log('='.repeat(80));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(60));
  log(`📋 ${message}`, 'yellow');
  console.log('-'.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results storage
const testResults = {
  startTime: new Date().toISOString(),
  endTime: null,
  duration: null,
  tests: {
    serverCheck: { status: 'pending', output: '', duration: 0 },
    endpoints: { status: 'pending', output: '', duration: 0 },
    configuration: { status: 'pending', output: '', duration: 0 },
    connections: { status: 'pending', output: '', duration: 0 }
  },
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  }
};

// Run a command and capture output
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const child = spawn(command, args, {
      cwd: process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      const duration = Date.now() - startTime;
      resolve({
        code,
        stdout,
        stderr,
        duration,
        success: code === 0
      });
    });

    child.on('error', (error) => {
      const duration = Date.now() - startTime;
      reject({
        error,
        duration,
        success: false
      });
    });
  });
}

// Check if server is running
async function checkServer() {
  logSubHeader('Server Status Check');
  
  try {
    const result = await runCommand('node', ['test-endpoints.js']);
    
    if (result.success) {
      logSuccess('Server is running and responding correctly');
      testResults.tests.serverCheck = {
        status: 'passed',
        output: result.stdout,
        duration: result.duration
      };
      return true;
    } else {
      logError('Server check failed');
      testResults.tests.serverCheck = {
        status: 'failed',
        output: result.stderr || result.stdout,
        duration: result.duration
      };
      return false;
    }
  } catch (error) {
    logError(`Server check error: ${error.message || error.error?.message}`);
    testResults.tests.serverCheck = {
      status: 'failed',
      output: error.message || error.error?.message,
      duration: error.duration || 0
    };
    return false;
  }
}

// Run configuration tests
async function runConfigurationTests() {
  logSubHeader('Configuration Testing');
  
  try {
    const result = await runCommand('node', ['test-database-config.js']);
    
    if (result.success) {
      logSuccess('Configuration tests passed');
      testResults.tests.configuration = {
        status: 'passed',
        output: result.stdout,
        duration: result.duration
      };
    } else {
      logWarning('Configuration tests had issues');
      testResults.tests.configuration = {
        status: 'warning',
        output: result.stderr || result.stdout,
        duration: result.duration
      };
    }
  } catch (error) {
    logError(`Configuration tests error: ${error.message || error.error?.message}`);
    testResults.tests.configuration = {
      status: 'failed',
      output: error.message || error.error?.message,
      duration: error.duration || 0
    };
  }
}

// Run connection tests
async function runConnectionTests() {
  logSubHeader('Connection Testing');
  
  try {
    const result = await runCommand('node', ['test-database.js']);
    
    if (result.success) {
      logSuccess('Connection tests passed');
      testResults.tests.connections = {
        status: 'passed',
        output: result.stdout,
        duration: result.duration
      };
    } else {
      logWarning('Connection tests had issues');
      testResults.tests.connections = {
        status: 'warning',
        output: result.stderr || result.stdout,
        duration: result.duration
      };
    }
  } catch (error) {
    logError(`Connection tests error: ${error.message || error.error?.message}`);
    testResults.tests.connections = {
      status: 'failed',
      output: error.message || error.error?.message,
      duration: error.duration || 0
    };
  }
}

// Generate summary
function generateSummary() {
  logSubHeader('Test Summary');
  
  const tests = Object.values(testResults.tests);
  testResults.summary.total = tests.length;
  testResults.summary.passed = tests.filter(t => t.status === 'passed').length;
  testResults.summary.failed = tests.filter(t => t.status === 'failed').length;
  testResults.summary.warnings = tests.filter(t => t.status === 'warning').length;
  
  const { total, passed, failed, warnings } = testResults.summary;
  
  logInfo(`Total Tests: ${total}`);
  logSuccess(`Passed: ${passed}`);
  logError(`Failed: ${failed}`);
  logWarning(`Warnings: ${warnings}`);
  
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
  logInfo(`Success Rate: ${successRate}%`);
  
  // Overall status
  if (failed === 0 && warnings === 0) {
    logSuccess('\n🎉 All tests passed! Database system is fully functional.');
  } else if (failed === 0) {
    logWarning('\n⚠️  All tests passed with some warnings. System is functional but needs attention.');
  } else {
    logError('\n❌ Some tests failed. Please check the issues above.');
  }
}

// Generate HTML report
function generateHTMLReport() {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test Report - SantriMental</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2563eb; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #2563eb; }
        .stat-card.passed { border-left-color: #10b981; }
        .stat-card.failed { border-left-color: #ef4444; }
        .stat-card.warning { border-left-color: #f59e0b; }
        .test { margin-bottom: 30px; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .test h3 { color: #374151; margin-top: 0; }
        .status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status.passed { background: #d1fae5; color: #065f46; }
        .status.failed { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .output { background: #f8fafc; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Database Test Report</h1>
            <h2>SantriMental - Multi-Provider Database System</h2>
            <p>Generated on: ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <h3>Total Tests</h3>
                <div style="font-size: 2em; font-weight: bold;">${testResults.summary.total}</div>
            </div>
            <div class="stat-card passed">
                <h3>Passed</h3>
                <div style="font-size: 2em; font-weight: bold; color: #10b981;">${testResults.summary.passed}</div>
            </div>
            <div class="stat-card failed">
                <h3>Failed</h3>
                <div style="font-size: 2em; font-weight: bold; color: #ef4444;">${testResults.summary.failed}</div>
            </div>
            <div class="stat-card warning">
                <h3>Warnings</h3>
                <div style="font-size: 2em; font-weight: bold; color: #f59e0b;">${testResults.summary.warnings}</div>
            </div>
        </div>
        
        ${Object.entries(testResults.tests).map(([name, test]) => `
        <div class="test">
            <h3>${name.charAt(0).toUpperCase() + name.slice(1).replace(/([A-Z])/g, ' $1')}</h3>
            <p>
                Status: <span class="status ${test.status}">${test.status}</span>
                Duration: ${test.duration}ms
            </p>
            <div class="output">${test.output || 'No output'}</div>
        </div>
        `).join('')}
        
        <div style="margin-top: 30px; text-align: center; color: #6b7280;">
            <p>Report generated by SantriMental Database Testing Suite</p>
            <p>Duration: ${testResults.duration ? (testResults.duration / 1000).toFixed(2) + ' seconds' : 'N/A'}</p>
        </div>
    </div>
</body>
</html>`;
  
  const htmlPath = path.join(__dirname, 'database-test-report.html');
  fs.writeFileSync(htmlPath, htmlContent);
  logInfo(`HTML report saved to: ${htmlPath}`);
}

// Main test runner
async function runAllTests() {
  logHeader('SantriMental Database Testing Suite');
  
  const startTime = Date.now();
  
  // Run tests in sequence
  const serverOk = await checkServer();
  
  if (serverOk) {
    await runConfigurationTests();
    await runConnectionTests();
  } else {
    logError('Skipping database tests - server is not running');
    logInfo('Please start the server with: npm run dev');
  }
  
  // Calculate duration
  const endTime = Date.now();
  testResults.endTime = new Date().toISOString();
  testResults.duration = endTime - startTime;
  
  // Generate reports
  generateSummary();
  
  // Save JSON report
  const jsonPath = path.join(__dirname, 'database-test-results.json');
  fs.writeFileSync(jsonPath, JSON.stringify(testResults, null, 2));
  logInfo(`JSON report saved to: ${jsonPath}`);
  
  // Generate HTML report
  generateHTMLReport();
  
  // Exit with appropriate code
  const exitCode = testResults.summary.failed > 0 ? 1 : 0;
  process.exit(exitCode);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    logError(`Test suite failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export { runAllTests, testResults };
