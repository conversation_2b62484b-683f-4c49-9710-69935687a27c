/**
 * 🛣️ Routes Registration
 * 
 * Central route registration for all API endpoints
 */

import { type Express } from "express";
import authRoutes from "./auth.js";
import adminDashboardRoutes from "./admin-dashboard.js";
import adminRoutes from "./admin.js";
import databaseRoutes from "./database.js";
import migrationRoutes from "./migration.js";

export function registerRoutes(app: Express): void {
  // Authentication routes
  app.use("/api/auth", authRoutes);
  
  // Admin dashboard routes (requires admin role)
  app.use("/api/admin-dashboard", adminDashboardRoutes);
  
  // Admin system routes (database management)
  app.use("/api/admin", adminRoutes);
  
  // Database management routes
  app.use("/api/database", databaseRoutes);
  
  // Migration routes
  app.use("/api/migration", migrationRoutes);

  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      message: "SantriMental API is running"
    });
  });

  // API status endpoint
  app.get("/api/status", (req, res) => {
    res.json({
      success: true,
      data: {
        service: "SantriMental API",
        version: "1.0.0",
        environment: process.env.NODE_ENV || "development",
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }
    });
  });
}
