import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, <PERSON><PERSON>resh<PERSON><PERSON>, CheckCircle, AlertCircle, Clock } from 'lucide-react';

interface SyncStatus {
  pending: number;
  lastSync: string | null;
  isOnline: boolean;
}

export function SyncIndicator() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    pending: 0,
    lastSync: null,
    isOnline: navigator.onLine
  });
  const [isLoading, setIsLoading] = useState(false);

  const fetchSyncStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/sync/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const status = await response.json();
        setSyncStatus(status);
      }
    } catch (error) {
      console.error('Failed to fetch sync status:', error);
    }
  };

  const triggerSync = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/sync/trigger', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        await fetchSyncStatus();
      }
    } catch (error) {
      console.error('Failed to trigger sync:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchSyncStatus();

    // Listen for online/offline events
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }));
      fetchSyncStatus();
    };

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Periodic status check
    const interval = setInterval(fetchSyncStatus, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  const getStatusIcon = () => {
    if (!syncStatus.isOnline) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }

    if (isLoading) {
      return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
    }

    if (syncStatus.pending > 0) {
      return <Clock className="w-4 h-4 text-yellow-500" />;
    }

    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!syncStatus.isOnline) {
      return 'Offline';
    }

    if (isLoading) {
      return 'Sinkronisasi...';
    }

    if (syncStatus.pending > 0) {
      return `${syncStatus.pending} menunggu sync`;
    }

    return 'Tersinkronisasi';
  };

  const getStatusColor = () => {
    if (!syncStatus.isOnline) return 'bg-red-500 border-red-600 text-white shadow-red-200';
    if (isLoading) return 'bg-blue-500 border-blue-600 text-white shadow-blue-200';
    if (syncStatus.pending > 0) return 'bg-amber-500 border-amber-600 text-white shadow-amber-200';
    return 'bg-emerald-500 border-emerald-600 text-white shadow-emerald-200';
  };

  const formatLastSync = (lastSync: string | null) => {
    if (!lastSync) return 'Belum pernah';
    
    const date = new Date(lastSync);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Baru saja';
    if (diffMins < 60) return `${diffMins} menit lalu`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} jam lalu`;
    
    return date.toLocaleDateString('id-ID');
  };

  return (
    <div className={`fixed top-4 right-4 z-40 px-3 py-2 rounded-full border shadow-lg transition-all duration-200 ${getStatusColor()}`}>
      <div className="flex items-center space-x-2">
        {getStatusIcon()}
        <div className="text-xs font-medium">
          {getStatusText()}
        </div>
      </div>
        
        {syncStatus.isOnline && syncStatus.pending > 0 && (
          <button
            onClick={triggerSync}
            disabled={isLoading}
            className="ml-2 p-1 rounded-full hover:bg-white/50 transition-colors"
            title="Sinkronisasi sekarang"
          >
            <RefreshCw className={`w-3 h-3 text-gray-600 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        )}
      </div>
      
      {/* Detailed status on hover */}
      <div className="absolute top-full right-0 mt-2 p-3 bg-white rounded-lg shadow-lg border opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto transition-opacity duration-200 min-w-64">
        <div className="text-sm space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">Status Koneksi:</span>
            <span className={`font-medium ${syncStatus.isOnline ? 'text-green-600' : 'text-red-600'}`}>
              {syncStatus.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600">Data Pending:</span>
            <span className="font-medium text-gray-900">
              {syncStatus.pending} item
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-600">Sync Terakhir:</span>
            <span className="font-medium text-gray-900">
              {formatLastSync(syncStatus.lastSync)}
            </span>
          </div>
          
          {syncStatus.isOnline && (
            <button
              onClick={triggerSync}
              disabled={isLoading}
              className="w-full mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Menyinkronkan...' : 'Sinkronisasi Manual'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

// Hook untuk menggunakan sync status
export function useSyncStatus() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    pending: 0,
    lastSync: null,
    isOnline: navigator.onLine
  });

  const fetchSyncStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/sync/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const status = await response.json();
        setSyncStatus(status);
      }
    } catch (error) {
      console.error('Failed to fetch sync status:', error);
    }
  };

  const triggerSync = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch('/api/sync/trigger', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        await fetchSyncStatus();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to trigger sync:', error);
      return false;
    }
  };

  useEffect(() => {
    fetchSyncStatus();
    
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }));
      fetchSyncStatus();
    };

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    const interval = setInterval(fetchSyncStatus, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  return {
    syncStatus,
    triggerSync,
    refreshStatus: fetchSyncStatus
  };
}
