import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  BookOpen, 
  Heart, 
  Brain, 
  Users, 
  Clock, 
  Star,
  Search,
  Filter,
  Play,
  Download,
  Share2
} from 'lucide-react';
import Navbar from '@/components/sections/Navbar';
import Footer from '@/components/sections/Footer';

const educationCategories = [
  {
    id: 'mental-health',
    title: 'Kesehatan Mental',
    description: 'Pemahaman dasar tentang kesehatan mental dalam perspektif Islam',
    icon: Brain,
    color: 'bg-blue-500',
    articles: 12
  },
  {
    id: 'stress-management',
    title: 'Manajemen Stres',
    description: 'Cara mengelola stres dan tekanan dalam kehidupan santri',
    icon: Heart,
    color: 'bg-green-500',
    articles: 8
  },
  {
    id: 'social-skills',
    title: 'Keterampilan Sosial',
    description: 'Membangun hubungan yang sehat dengan sesama santri',
    icon: Users,
    color: 'bg-purple-500',
    articles: 6
  },
  {
    id: 'islamic-psychology',
    title: 'Psikologi Islam',
    description: 'Pendekatan psikologi dalam ajaran Islam',
    icon: BookOpen,
    color: 'bg-emerald-500',
    articles: 10
  }
];

const featuredArticles = [
  {
    id: 1,
    title: 'Mengatasi Kecemasan dengan Dzikir dan Doa',
    description: 'Bagaimana dzikir dan doa dapat membantu menenangkan pikiran dan mengurangi kecemasan',
    category: 'Kesehatan Mental',
    readTime: '5 menit',
    rating: 4.8,
    image: '/api/placeholder/400/200',
    author: 'Dr. Ahmad Faiz, M.Psi',
    publishDate: '2 hari yang lalu'
  },
  {
    id: 2,
    title: 'Membangun Resiliensi Mental Santri',
    description: 'Strategi membangun ketahanan mental dalam menghadapi tantangan kehidupan pesantren',
    category: 'Manajemen Stres',
    readTime: '7 menit',
    rating: 4.9,
    image: '/api/placeholder/400/200',
    author: 'Ustadzah Fatimah, S.Psi',
    publishDate: '1 minggu yang lalu'
  },
  {
    id: 3,
    title: 'Komunikasi Efektif dalam Komunitas Pesantren',
    description: 'Tips berkomunikasi yang baik dengan ustadz, teman, dan keluarga',
    category: 'Keterampilan Sosial',
    readTime: '6 menit',
    rating: 4.7,
    image: '/api/placeholder/400/200',
    author: 'Ustadz Muhammad Ridwan, M.A',
    publishDate: '3 hari yang lalu'
  }
];

export default function Education() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/10 to-emerald-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Pusat Edukasi
              <span className="block text-primary">Kesehatan Mental Santri</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Pelajari tentang kesehatan mental dari perspektif Islam. 
              Temukan artikel, panduan, dan sumber daya yang membantu Anda memahami dan menjaga kesehatan jiwa.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Cari artikel, panduan, atau topik..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg rounded-full border-2 border-primary/20 focus:border-primary"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Kategori Pembelajaran</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Pilih kategori yang sesuai dengan kebutuhan pembelajaran Anda
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {educationCategories.map((category) => (
              <Card 
                key={category.id} 
                className="hover:shadow-lg transition-all duration-300 cursor-pointer group hover:-translate-y-1"
                onClick={() => setSelectedCategory(category.id)}
              >
                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 ${category.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                    <category.icon className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-lg">{category.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {category.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Badge variant="secondary" className="bg-primary/10 text-primary">
                    {category.articles} artikel
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Artikel Pilihan</h2>
              <p className="text-gray-600">Artikel terpopuler dan terbaru untuk Anda</p>
            </div>
            <Button variant="outline" className="gap-2">
              <Filter className="w-4 h-4" />
              Filter
            </Button>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredArticles.map((article) => (
              <Card key={article.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group">
                <div className="aspect-video bg-gradient-to-br from-primary/20 to-emerald-100 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-white/90 text-gray-800">
                      {article.category}
                    </Badge>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button size="lg" className="rounded-full">
                      <Play className="w-5 h-5 mr-2" />
                      Baca Artikel
                    </Button>
                  </div>
                </div>
                
                <CardHeader>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      {article.readTime}
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      {article.rating}
                    </div>
                  </div>
                  <CardTitle className="text-xl leading-tight group-hover:text-primary transition-colors">
                    {article.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600 leading-relaxed">
                    {article.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      <p className="font-medium">{article.author}</p>
                      <p>{article.publishDate}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Share2 className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button size="lg" className="gap-2">
              <BookOpen className="w-5 h-5" />
              Lihat Semua Artikel
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary to-emerald-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Mulai Perjalanan Pembelajaran Anda</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan santri lainnya yang telah merasakan manfaat dari program edukasi kesehatan mental kami
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button size="lg" variant="secondary" className="gap-2">
                <Users className="w-5 h-5" />
                Daftar Sekarang
              </Button>
            </Link>
            <Link href="/assessments">
              <Button size="lg" variant="outline" className="gap-2 text-white border-white hover:bg-white hover:text-primary">
                <Brain className="w-5 h-5" />
                Mulai Assessment
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
