import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Navbar } from '@/components/sections/Navbar';
import { Footer } from '@/components/sections/Footer';
import { Play, Search, Trophy, Users, Clock, Star, Gamepad2, Brain, Heart, Zap } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Game {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  category: string;
  difficulty: 'Mudah' | 'Sedang' | 'Sulit';
  duration: string;
  players: string;
  rating: number;
  playCount: string;
  tags: string[];
  gameUrl: string;
  isNew?: boolean;
  isPopular?: boolean;
}

const games: Game[] = [
  {
    id: '1',
    title: 'Mood Tracker Challenge',
    description: 'Game interaktif untuk memantau dan memahami perubahan mood harian dengan cara yang menyenangkan.',
    thumbnail: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=300&fit=crop',
    category: 'Monitoring',
    difficulty: 'Mudah',
    duration: '10-15 menit',
    players: '1 pemain',
    rating: 4.8,
    playCount: '2.1K',
    tags: ['mood', 'tracking', 'harian', 'emosi'],
    gameUrl: '/games/mood-tracker',
    isNew: true
  },
  {
    id: '2',
    title: 'Relaksasi Virtual Garden',
    description: 'Simulasi berkebun virtual yang membantu relaksasi dan mengurangi stres dengan suasana yang menenangkan.',
    thumbnail: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop',
    category: 'Relaksasi',
    difficulty: 'Mudah',
    duration: '15-30 menit',
    players: '1 pemain',
    rating: 4.9,
    playCount: '3.5K',
    tags: ['relaksasi', 'berkebun', 'virtual', 'stres'],
    gameUrl: '/games/virtual-garden',
    isPopular: true
  },
  {
    id: '3',
    title: 'Memory Palace Builder',
    description: 'Game puzzle untuk melatih memori dan konsentrasi dengan teknik istana memori yang efektif.',
    thumbnail: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=300&fit=crop',
    category: 'Kognitif',
    difficulty: 'Sedang',
    duration: '20-25 menit',
    players: '1-2 pemain',
    rating: 4.6,
    playCount: '1.8K',
    tags: ['memori', 'puzzle', 'konsentrasi', 'kognitif'],
    gameUrl: '/games/memory-palace'
  },
  {
    id: '4',
    title: 'Breathing Rhythm Master',
    description: 'Game ritme yang mengajarkan teknik pernapasan untuk mengatasi kecemasan dan meningkatkan fokus.',
    thumbnail: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop',
    category: 'Terapi',
    difficulty: 'Mudah',
    duration: '5-10 menit',
    players: '1 pemain',
    rating: 4.7,
    playCount: '4.2K',
    tags: ['pernapasan', 'ritme', 'kecemasan', 'fokus'],
    gameUrl: '/games/breathing-rhythm',
    isPopular: true
  },
  {
    id: '5',
    title: 'Empathy Quest',
    description: 'RPG sederhana yang mengajarkan empati dan keterampilan sosial melalui berbagai skenario interaksi.',
    thumbnail: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=300&fit=crop',
    category: 'Sosial',
    difficulty: 'Sedang',
    duration: '30-45 menit',
    players: '1-4 pemain',
    rating: 4.5,
    playCount: '1.3K',
    tags: ['empati', 'sosial', 'rpg', 'interaksi'],
    gameUrl: '/games/empathy-quest'
  },
  {
    id: '6',
    title: 'Mindfulness Maze',
    description: 'Labirin interaktif yang mengajarkan teknik mindfulness dan kesadaran penuh dalam setiap langkah.',
    thumbnail: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
    category: 'Mindfulness',
    difficulty: 'Sulit',
    duration: '25-40 menit',
    players: '1 pemain',
    rating: 4.8,
    playCount: '967',
    tags: ['mindfulness', 'labirin', 'kesadaran', 'meditasi'],
    gameUrl: '/games/mindfulness-maze',
    isNew: true
  }
];

const categories = ['Semua', 'Monitoring', 'Relaksasi', 'Kognitif', 'Terapi', 'Sosial', 'Mindfulness'];
const difficulties = ['Semua', 'Mudah', 'Sedang', 'Sulit'];

export default function Game() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Semua');
  const [selectedDifficulty, setSelectedDifficulty] = useState('Semua');
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);

  const filteredGames = games.filter(game => {
    const matchesSearch = game.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         game.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         game.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'Semua' || game.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'Semua' || game.difficulty === selectedDifficulty;
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const handlePlayGame = (game: Game) => {
    setSelectedGame(game);
    toast({
      title: 'Game Dimulai',
      description: `Memulai "${game.title}"...`
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Mudah': return 'bg-green-100 text-green-800';
      case 'Sedang': return 'bg-yellow-100 text-yellow-800';
      case 'Sulit': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-4">
            🎮 Games Edukasi SantriMental
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Koleksi games interaktif untuk melatih kesehatan mental dengan cara yang menyenangkan
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Gamepad2 className="w-8 h-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{games.length}</div>
              <div className="text-sm text-muted-foreground">Total Games</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Users className="w-8 h-8 mx-auto mb-2 text-emerald-600" />
              <div className="text-2xl font-bold">12.8K</div>
              <div className="text-sm text-muted-foreground">Total Pemain</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Trophy className="w-8 h-8 mx-auto mb-2 text-yellow-600" />
              <div className="text-2xl font-bold">4.7</div>
              <div className="text-sm text-muted-foreground">Rating Rata-rata</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Zap className="w-8 h-8 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold">85%</div>
              <div className="text-sm text-muted-foreground">Tingkat Kepuasan</div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 space-y-4">
          <div className="relative max-w-md mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Cari games edukasi..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
          
          <div className="flex flex-wrap justify-center gap-2">
            {difficulties.map((difficulty) => (
              <Button
                key={difficulty}
                variant={selectedDifficulty === difficulty ? "secondary" : "outline"}
                size="sm"
                onClick={() => setSelectedDifficulty(difficulty)}
                className="rounded-full"
              >
                {difficulty}
              </Button>
            ))}
          </div>
        </div>

        {/* Game Player Modal */}
        {selectedGame && (
          <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
            <div className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-bold">{selectedGame.title}</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedGame(null)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    ✕
                  </Button>
                </div>
                
                <div className="aspect-video mb-4 bg-muted rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Gamepad2 className="w-16 h-16 mx-auto mb-4 text-primary" />
                    <p className="text-muted-foreground mb-2">Game Player Placeholder</p>
                    <p className="text-sm text-muted-foreground">
                      URL: {selectedGame.gameUrl}
                    </p>
                    <div className="mt-4">
                      <Progress value={33} className="w-64 mx-auto" />
                      <p className="text-xs text-muted-foreground mt-2">Loading game...</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {selectedGame.players}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {selectedGame.duration}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(selectedGame.difficulty)}`}>
                      {selectedGame.difficulty}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="flex">{renderStars(selectedGame.rating)}</div>
                    <span className="text-sm text-muted-foreground">
                      ({selectedGame.rating}/5.0) • {selectedGame.playCount} plays
                    </span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Badge variant="secondary">{selectedGame.category}</Badge>
                    {selectedGame.tags.map((tag) => (
                      <Badge key={tag} variant="outline">#{tag}</Badge>
                    ))}
                  </div>
                  
                  <p className="text-muted-foreground">{selectedGame.description}</p>
                  
                  <div className="flex gap-2">
                    <Button className="gap-2">
                      <Play className="w-4 h-4" />
                      Mulai Game
                    </Button>
                    <Button variant="outline" className="gap-2">
                      <Heart className="w-4 h-4" />
                      Favorit
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Games Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGames.map((game) => (
            <Card key={game.id} className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
              <div className="relative overflow-hidden rounded-t-lg">
                <img
                  src={game.thumbnail}
                  alt={game.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button
                    size="lg"
                    className="rounded-full"
                    onClick={() => handlePlayGame(game)}
                  >
                    <Play className="w-6 h-6 mr-2" />
                    Main
                  </Button>
                </div>
                
                {/* Badges */}
                <div className="absolute top-2 left-2 flex gap-1">
                  {game.isNew && (
                    <Badge className="bg-green-500 hover:bg-green-600 text-xs">Baru</Badge>
                  )}
                  {game.isPopular && (
                    <Badge className="bg-orange-500 hover:bg-orange-600 text-xs">Populer</Badge>
                  )}
                </div>
                
                <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs ${getDifficultyColor(game.difficulty)}`}>
                  {game.difficulty}
                </div>
              </div>
              
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary" className="text-xs">{game.category}</Badge>
                  <div className="flex items-center gap-1">
                    <div className="flex">{renderStars(game.rating)}</div>
                    <span className="text-xs text-muted-foreground ml-1">
                      ({game.rating})
                    </span>
                  </div>
                </div>
                <CardTitle className="text-lg line-clamp-2 group-hover:text-primary transition-colors">
                  {game.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="pt-0">
                <CardDescription className="line-clamp-2 mb-4">
                  {game.description}
                </CardDescription>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {game.players}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {game.duration}
                  </span>
                  <span className="flex items-center gap-1">
                    <Trophy className="w-4 h-4" />
                    {game.playCount}
                  </span>
                </div>
                
                <Button
                  onClick={() => handlePlayGame(game)}
                  className="w-full gap-2"
                >
                  <Play className="w-4 h-4" />
                  Main Sekarang
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredGames.length === 0 && (
          <div className="text-center py-12">
            <Gamepad2 className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-xl font-semibold mb-2">Tidak ada game ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah kata kunci pencarian atau pilih kategori lain
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
