import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from "@shared/schema";
import path from 'path';
import fs from 'fs';

// SQLite database for offline storage
const dbPath = path.join(process.cwd(), 'data', 'santrimental.db');

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const sqlite = new Database(dbPath);

// Enable WAL mode for better performance
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('synchronous = NORMAL');
sqlite.pragma('cache_size = 1000000');
sqlite.pragma('foreign_keys = ON');

export const sqliteDb = drizzle(sqlite, { schema });
export const rawSqlite = sqlite;

// Initialize SQLite tables
export function initSQLiteTables() {
  try {
    // Create tables if they don't exist
    sqlite.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced BOOLEAN DEFAULT FALSE,
        last_sync DATETIME
      );

      CREATE TABLE IF NOT EXISTS profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        nama_lengkap TEXT NOT NULL,
        nomor_induk TEXT,
        jenis_kelamin TEXT,
        tanggal_lahir TEXT,
        kelas TEXT,
        pondok_pesantren TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced BOOLEAN DEFAULT FALSE,
        last_sync DATETIME,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );

      CREATE TABLE IF NOT EXISTS assessment_configs (
        id TEXT PRIMARY KEY,
        assessment_code TEXT NOT NULL UNIQUE,
        assessment_name TEXT NOT NULL,
        description TEXT,
        version TEXT DEFAULT '1.0',
        total_items INTEGER NOT NULL,
        estimated_time_minutes INTEGER DEFAULT 15,
        is_active BOOLEAN DEFAULT TRUE,
        requires_supervision BOOLEAN DEFAULT FALSE,
        age_min INTEGER DEFAULT 12,
        age_max INTEGER DEFAULT 30,
        cultural_context TEXT DEFAULT 'pesantren',
        scoring_method TEXT NOT NULL,
        clinical_cutoffs TEXT,
        psychometric_properties TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced BOOLEAN DEFAULT FALSE,
        last_sync DATETIME
      );

      CREATE TABLE IF NOT EXISTS assessment_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        assessment_code TEXT NOT NULL,
        session_status TEXT DEFAULT 'started',
        started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        duration_seconds INTEGER,
        ip_address TEXT,
        user_agent TEXT,
        device_type TEXT,
        is_supervised BOOLEAN DEFAULT FALSE,
        supervisor_id TEXT,
        notes TEXT,
        synced BOOLEAN DEFAULT FALSE,
        last_sync DATETIME,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );

      CREATE TABLE IF NOT EXISTS assessment_results (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        assessment_code TEXT NOT NULL,
        user_id TEXT NOT NULL,
        total_raw_score TEXT,
        domain_scores TEXT,
        total_t_score TEXT,
        total_percentile TEXT,
        domain_t_scores TEXT,
        domain_percentiles TEXT,
        overall_severity TEXT,
        domain_severities TEXT,
        risk_level TEXT DEFAULT 'low',
        interpretation_summary TEXT,
        clinical_recommendations TEXT,
        referral_recommended BOOLEAN DEFAULT FALSE,
        follow_up_recommended BOOLEAN DEFAULT FALSE,
        follow_up_timeframe TEXT,
        reliability_alpha TEXT,
        response_consistency TEXT,
        completion_percentage TEXT DEFAULT '100.00',
        validity_flags TEXT,
        religious_coping_indicators TEXT,
        cultural_considerations TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced BOOLEAN DEFAULT FALSE,
        last_sync DATETIME,
        FOREIGN KEY (session_id) REFERENCES assessment_sessions(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
      );

      CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE'
        data TEXT, -- JSON data
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        retry_count INTEGER DEFAULT 0,
        last_error TEXT
      );

      CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
      CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);
      CREATE INDEX IF NOT EXISTS idx_assessment_sessions_user_id ON assessment_sessions(user_id);
      CREATE INDEX IF NOT EXISTS idx_assessment_results_user_id ON assessment_results(user_id);
      CREATE INDEX IF NOT EXISTS idx_sync_queue_table ON sync_queue(table_name);
      CREATE INDEX IF NOT EXISTS idx_sync_queue_operation ON sync_queue(operation);
    `);

    console.log('✅ SQLite tables initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing SQLite tables:', error);
    throw error;
  }
}

// Initialize on import
initSQLiteTables();
