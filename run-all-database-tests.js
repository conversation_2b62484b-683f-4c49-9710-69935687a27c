#!/usr/bin/env node

/**
 * 🚀 Master Database Testing Suite - SantriMental
 * 
 * Runs all database tests in sequence:
 * 1. Configuration Testing
 * 2. Connection Testing  
 * 3. Migration Testing
 * 4. Performance Testing
 * 5. Comprehensive Report Generation
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import axios from 'axios';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import test modules
import { runConfigTests, configResults } from './test-database-config.js';
import { runAllTests, testResults } from './test-database.js';
import { runMigrationTests, migrationResults } from './test-database-migration.js';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(80));
  log(`🚀 ${message}`, 'cyan');
  console.log('='.repeat(80));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(60));
  log(`📋 ${message}`, 'yellow');
  console.log('-'.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Master test results
const masterResults = {
  startTime: new Date().toISOString(),
  endTime: null,
  duration: null,
  suites: {
    configuration: { status: 'pending', results: null, duration: null },
    connections: { status: 'pending', results: null, duration: null },
    migrations: { status: 'pending', results: null, duration: null }
  },
  summary: {
    totalTests: 0,
    totalPassed: 0,
    totalFailed: 0,
    totalWarnings: 0,
    overallStatus: 'pending'
  },
  recommendations: []
};

// Check if server is running
async function checkServerStatus() {
  logSubHeader('Pre-flight Check');

  try {
    await axios.get('http://localhost:5000/health', { timeout: 5000 });
    logSuccess('Server is running ✅');
    return true;
  } catch (error) {
    logError('Server is not running ❌');
    logError('Please start the server first: npm run dev');
    return false;
  }
}

// Run configuration tests
async function runConfigurationTests() {
  logHeader('1. Configuration Testing Suite');
  
  const startTime = Date.now();
  
  try {
    await runConfigTests();
    
    const duration = Date.now() - startTime;
    masterResults.suites.configuration = {
      status: 'completed',
      results: configResults,
      duration: duration
    };
    
    logSuccess(`Configuration tests completed in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    masterResults.suites.configuration = {
      status: 'failed',
      results: null,
      duration: duration,
      error: error.message
    };
    
    logError(`Configuration tests failed: ${error.message}`);
  }
}

// Run connection tests
async function runConnectionTests() {
  logHeader('2. Connection Testing Suite');
  
  const startTime = Date.now();
  
  try {
    await runAllTests();
    
    const duration = Date.now() - startTime;
    masterResults.suites.connections = {
      status: 'completed',
      results: testResults,
      duration: duration
    };
    
    logSuccess(`Connection tests completed in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    masterResults.suites.connections = {
      status: 'failed',
      results: null,
      duration: duration,
      error: error.message
    };
    
    logError(`Connection tests failed: ${error.message}`);
  }
}

// Run migration tests
async function runMigrationTestSuite() {
  logHeader('3. Migration Testing Suite');
  
  const startTime = Date.now();
  
  try {
    await runMigrationTests();
    
    const duration = Date.now() - startTime;
    masterResults.suites.migrations = {
      status: 'completed',
      results: migrationResults,
      duration: duration
    };
    
    logSuccess(`Migration tests completed in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    masterResults.suites.migrations = {
      status: 'failed',
      results: null,
      duration: duration,
      error: error.message
    };
    
    logError(`Migration tests failed: ${error.message}`);
  }
}

// Aggregate results from all test suites
function aggregateResults() {
  logSubHeader('Aggregating Results');
  
  let totalTests = 0;
  let totalPassed = 0;
  let totalFailed = 0;
  let totalWarnings = 0;
  
  // Aggregate from configuration tests
  if (masterResults.suites.configuration.results) {
    const configSummary = masterResults.suites.configuration.results.summary;
    totalTests += configSummary.total;
    totalPassed += configSummary.passed;
    totalFailed += configSummary.failed;
    totalWarnings += configSummary.warnings;
  }
  
  // Aggregate from connection tests
  if (masterResults.suites.connections.results) {
    const connectionSummary = masterResults.suites.connections.results.summary;
    totalTests += connectionSummary.total;
    totalPassed += connectionSummary.passed;
    totalFailed += connectionSummary.failed;
    totalWarnings += connectionSummary.warnings;
  }
  
  // Aggregate from migration tests
  if (masterResults.suites.migrations.results) {
    const migrationSummary = masterResults.suites.migrations.results.summary;
    totalTests += migrationSummary.total;
    totalPassed += migrationSummary.passed;
    totalFailed += migrationSummary.failed;
    totalWarnings += migrationSummary.warnings;
  }
  
  masterResults.summary = {
    totalTests,
    totalPassed,
    totalFailed,
    totalWarnings,
    overallStatus: totalFailed === 0 ? (totalWarnings === 0 ? 'excellent' : 'good') : 'needs-attention'
  };
  
  logInfo(`Total Tests: ${totalTests}`);
  logSuccess(`Passed: ${totalPassed}`);
  logError(`Failed: ${totalFailed}`);
  logWarning(`Warnings: ${totalWarnings}`);
}

// Generate recommendations
function generateRecommendations() {
  logSubHeader('Generating Recommendations');
  
  const recommendations = [];
  
  // Check configuration issues
  if (masterResults.suites.configuration.results) {
    const configResults = masterResults.suites.configuration.results;
    
    if (configResults.summary.failed > 0) {
      recommendations.push({
        priority: 'high',
        category: 'configuration',
        message: 'Critical configuration issues found. Please fix environment variables and database URLs.',
        action: 'Review .env file and ensure all required database configurations are set.'
      });
    }
    
    if (configResults.environment && configResults.environment['database-configured']?.status === 'failed') {
      recommendations.push({
        priority: 'high',
        category: 'configuration',
        message: 'No database is configured.',
        action: 'Set at least one database URL in your .env file (DATABASE_URL, AIVEN_MYSQL_URL, or NEON_DATABASE_URL).'
      });
    }
  }
  
  // Check connection issues
  if (masterResults.suites.connections.results) {
    const connectionResults = masterResults.suites.connections.results;
    
    if (connectionResults.summary.failed > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'connections',
        message: 'Some database connections are failing.',
        action: 'Check database server status and network connectivity. Verify credentials and SSL certificates.'
      });
    }
  }
  
  // Check migration issues
  if (masterResults.suites.migrations.results) {
    const migrationResults = masterResults.suites.migrations.results;
    
    if (migrationResults.summary.warnings > 0) {
      recommendations.push({
        priority: 'low',
        category: 'migrations',
        message: 'Migration features are not fully implemented.',
        action: 'Consider implementing backup and migration APIs for production use.'
      });
    }
  }
  
  // Performance recommendations
  if (masterResults.suites.connections.results?.performance) {
    const perfResults = masterResults.suites.connections.results.performance;
    const slowTests = Object.entries(perfResults).filter(([_, result]) => 
      result.status === 'failed' || result.details?.includes('Slow')
    );
    
    if (slowTests.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'performance',
        message: 'Some database operations are slow.',
        action: 'Consider optimizing database queries, adding indexes, or upgrading database resources.'
      });
    }
  }
  
  masterResults.recommendations = recommendations;
  
  // Display recommendations
  if (recommendations.length === 0) {
    logSuccess('No recommendations needed - everything looks great! 🎉');
  } else {
    recommendations.forEach((rec, index) => {
      const priorityColor = rec.priority === 'high' ? 'red' : rec.priority === 'medium' ? 'yellow' : 'blue';
      log(`\n${index + 1}. [${rec.priority.toUpperCase()}] ${rec.category.toUpperCase()}`, priorityColor);
      log(`   Issue: ${rec.message}`);
      log(`   Action: ${rec.action}`);
    });
  }
}

// Generate comprehensive report
function generateComprehensiveReport() {
  logHeader('Comprehensive Database Test Report');
  
  const endTime = new Date();
  masterResults.endTime = endTime.toISOString();
  masterResults.duration = endTime - new Date(masterResults.startTime);
  
  // Display summary
  const { totalTests, totalPassed, totalFailed, totalWarnings, overallStatus } = masterResults.summary;
  
  logInfo(`Test Duration: ${(masterResults.duration / 1000).toFixed(2)} seconds`);
  logInfo(`Total Tests: ${totalTests}`);
  logSuccess(`Passed: ${totalPassed}`);
  logError(`Failed: ${totalFailed}`);
  logWarning(`Warnings: ${totalWarnings}`);
  
  const successRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0;
  logInfo(`Success Rate: ${successRate}%`);
  
  // Overall status
  switch (overallStatus) {
    case 'excellent':
      logSuccess('\n🎉 EXCELLENT! All database systems are working perfectly!');
      break;
    case 'good':
      logWarning('\n✅ GOOD! Database systems are working with minor issues.');
      break;
    case 'needs-attention':
      logError('\n⚠️  NEEDS ATTENTION! Some database systems have issues that need to be fixed.');
      break;
  }
  
  // Save comprehensive report
  const reportPath = path.join(__dirname, 'comprehensive-database-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(masterResults, null, 2));
  logInfo(`\nComprehensive report saved to: ${reportPath}`);
  
  // Generate HTML report
  generateHTMLReport();
}

// Generate HTML report
function generateHTMLReport() {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test Report - SantriMental</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2563eb; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #2563eb; }
        .stat-card.passed { border-left-color: #10b981; }
        .stat-card.failed { border-left-color: #ef4444; }
        .stat-card.warning { border-left-color: #f59e0b; }
        .suite { margin-bottom: 30px; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .suite h3 { color: #374151; margin-top: 0; }
        .status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status.completed { background: #d1fae5; color: #065f46; }
        .status.failed { background: #fee2e2; color: #991b1b; }
        .recommendations { background: #fef3c7; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .rec-item { margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; }
        .rec-high { border-left: 4px solid #ef4444; }
        .rec-medium { border-left: 4px solid #f59e0b; }
        .rec-low { border-left: 4px solid #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Database Test Report</h1>
            <h2>SantriMental - Multi-Provider Database System</h2>
            <p>Generated on: ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="stat-card">
                <h3>Total Tests</h3>
                <div style="font-size: 2em; font-weight: bold;">${masterResults.summary.totalTests}</div>
            </div>
            <div class="stat-card passed">
                <h3>Passed</h3>
                <div style="font-size: 2em; font-weight: bold; color: #10b981;">${masterResults.summary.totalPassed}</div>
            </div>
            <div class="stat-card failed">
                <h3>Failed</h3>
                <div style="font-size: 2em; font-weight: bold; color: #ef4444;">${masterResults.summary.totalFailed}</div>
            </div>
            <div class="stat-card warning">
                <h3>Warnings</h3>
                <div style="font-size: 2em; font-weight: bold; color: #f59e0b;">${masterResults.summary.totalWarnings}</div>
            </div>
        </div>
        
        <div class="suite">
            <h3>📋 Test Suites</h3>
            ${Object.entries(masterResults.suites).map(([name, suite]) => `
                <div style="margin-bottom: 15px;">
                    <strong>${name.charAt(0).toUpperCase() + name.slice(1)}:</strong>
                    <span class="status ${suite.status}">${suite.status}</span>
                    ${suite.duration ? `<span style="margin-left: 10px; color: #6b7280;">(${suite.duration}ms)</span>` : ''}
                </div>
            `).join('')}
        </div>
        
        ${masterResults.recommendations.length > 0 ? `
        <div class="recommendations">
            <h3>💡 Recommendations</h3>
            ${masterResults.recommendations.map((rec, index) => `
                <div class="rec-item rec-${rec.priority}">
                    <strong>[${rec.priority.toUpperCase()}] ${rec.category.toUpperCase()}</strong><br>
                    <strong>Issue:</strong> ${rec.message}<br>
                    <strong>Action:</strong> ${rec.action}
                </div>
            `).join('')}
        </div>
        ` : '<div class="recommendations"><h3>🎉 No recommendations needed - everything looks great!</h3></div>'}
        
        <div style="margin-top: 30px; text-align: center; color: #6b7280;">
            <p>Report generated by SantriMental Database Testing Suite</p>
            <p>Duration: ${(masterResults.duration / 1000).toFixed(2)} seconds</p>
        </div>
    </div>
</body>
</html>`;
  
  const htmlPath = path.join(__dirname, 'database-test-report.html');
  fs.writeFileSync(htmlPath, htmlContent);
  logInfo(`HTML report saved to: ${htmlPath}`);
  logInfo('Open the HTML file in your browser to view the detailed report.');
}

// Main test runner
async function runAllDatabaseTests() {
  logHeader('SantriMental - Complete Database Testing Suite');

  // Pre-flight check
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    process.exit(1);
  }

  // Run all test suites
  await runConfigurationTests();
  await runConnectionTests();
  await runMigrationTestSuite();

  // Process results
  aggregateResults();
  generateRecommendations();
  generateComprehensiveReport();

  // Exit with appropriate code
  const exitCode = masterResults.summary.totalFailed > 0 ? 1 : 0;
  process.exit(exitCode);
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllDatabaseTests().catch(error => {
    logError(`Master test suite failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export {
  runAllDatabaseTests,
  masterResults
};
