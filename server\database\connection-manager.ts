import { DatabaseFactory } from './factory';
import { getDatabaseConfig, DatabaseConfig, DatabaseProvider } from '../config/database';

export class ConnectionManager {
  private static instance: ConnectionManager;
  private connections: Map<DatabaseProvider, any> = new Map();
  private currentProvider: DatabaseProvider | null = null;

  private constructor() {}

  static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager();
    }
    return ConnectionManager.instance;
  }

  async initialize(): Promise<void> {
    const config = getDatabaseConfig();
    console.log(`🗄️ Initializing database: ${config.provider}`);

    try {
      const connection = await DatabaseFactory.create(config);
      this.connections.set(config.provider, connection);
      this.currentProvider = config.provider;

      console.log(`✅ Database ${config.provider} initialized successfully`);
    } catch (error) {
      console.error(`❌ Failed to initialize database ${config.provider}:`, error);

      // Fallback to SQLite if primary database fails
      if (config.provider !== 'sqlite') {
        console.log('🔄 Falling back to SQLite...');
        const fallbackConfig: DatabaseConfig = {
          provider: 'sqlite',
          url: './data/santrimental.db',
          ssl: false
        };

        try {
          const fallbackConnection = await DatabaseFactory.create(fallbackConfig);
          this.connections.set('sqlite', fallbackConnection);
          this.currentProvider = 'sqlite';
          console.log('✅ SQLite fallback initialized successfully');
        } catch (fallbackError) {
          console.error('❌ SQLite fallback also failed:', fallbackError);
          throw new Error('All database connections failed');
        }
      } else {
        throw error;
      }
    }
  }

  async getConnection(provider?: DatabaseProvider) {
    const targetProvider = provider || this.currentProvider;

    if (!targetProvider) {
      // Initialize if not done yet
      await this.initialize();
      return this.connections.get(this.currentProvider!);
    }

    const connection = this.connections.get(targetProvider);
    if (!connection) {
      throw new Error(`No connection available for provider: ${targetProvider}`);
    }

    return connection;
  }

  getCurrentProvider(): DatabaseProvider | null {
    return this.currentProvider;
  }

  async switchProvider(provider: DatabaseProvider): Promise<void> {
    if (this.connections.has(provider)) {
      this.currentProvider = provider;
      console.log(`🔄 Switched to database provider: ${provider}`);
      return;
    }

    // Initialize new provider if not exists
    const configs = this.getProviderConfigs();
    const config = configs.find(c => c.provider === provider);

    if (!config) {
      throw new Error(`No configuration found for provider: ${provider}`);
    }

    try {
      const connection = await DatabaseFactory.create(config);
      this.connections.set(provider, connection);
      this.currentProvider = provider;
      console.log(`✅ Switched to database provider: ${provider}`);
    } catch (error) {
      console.error(`❌ Failed to switch to provider ${provider}:`, error);
      throw error;
    }
  }

  private getProviderConfigs(): DatabaseConfig[] {
    const configs: DatabaseConfig[] = [];

    // AIVEN MySQL
    if (process.env.AIVEN_MYSQL_URL) {
      configs.push({
        provider: 'aiven-mysql',
        url: process.env.AIVEN_MYSQL_URL,
        ssl: {
          rejectUnauthorized: true,
          ca: process.env.AIVEN_CA_CERT
        },
        poolConfig: {
          connectionLimit: 20,
          queueLimit: 0,
          acquireTimeout: 60000,
          timeout: 60000
        }
      });
    }

    // NEON PostgreSQL
    if (process.env.NEON_DATABASE_URL) {
      configs.push({
        provider: 'neon-postgresql',
        url: process.env.NEON_DATABASE_URL,
        ssl: true,
        poolConfig: { connectionLimit: 20 }
      });
    }

    // Local MySQL
    if (process.env.DATABASE_URL?.includes('mysql')) {
      configs.push({
        provider: 'mysql',
        url: process.env.DATABASE_URL,
        ssl: false,
        poolConfig: { connectionLimit: 10 }
      });
    }

    // SQLite (always available)
    configs.push({
      provider: 'sqlite',
      url: './data/santrimental.db',
      ssl: false
    });

    return configs;
  }

  async closeAll(): Promise<void> {
    for (const [provider, connection] of this.connections.entries()) {
      try {
        if (connection.close) {
          await connection.close();
        }
        console.log(`✅ Closed connection: ${provider}`);
      } catch (error) {
        console.error(`❌ Failed to close connection ${provider}:`, error);
      }
    }

    this.connections.clear();
    this.currentProvider = null;
  }

  getHealthStatus() {
    return {
      currentProvider: this.currentProvider,
      availableProviders: Array.from(this.connections.keys()),
      connectionCount: this.connections.size,
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const connectionManager = ConnectionManager.getInstance();