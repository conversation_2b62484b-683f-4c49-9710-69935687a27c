#!/usr/bin/env node

/**
 * 🔄 Simple Database Migration
 * 
 * Adds role-based access control to existing database
 */

import 'dotenv/config';
import Database from 'better-sqlite3';
import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔄 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function detectDatabaseProvider() {
  if (process.env.AIVEN_MYSQL_URL) {
    return { provider: 'aiven-mysql', url: process.env.AIVEN_MYSQL_URL };
  } else if (process.env.DATABASE_URL && process.env.DATABASE_URL.includes('mysql')) {
    return { provider: 'mysql', url: process.env.DATABASE_URL };
  } else if (process.env.NEON_DATABASE_URL) {
    return { provider: 'neon-postgresql', url: process.env.NEON_DATABASE_URL };
  } else {
    return { provider: 'sqlite', url: './data/santrimental.db' };
  }
}

async function runSQLiteMigration(dbPath) {
  logInfo('Running SQLite migration...');
  
  // Ensure data directory exists
  const dataDir = path.dirname(dbPath);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
    logInfo('Created data directory');
  }
  
  const db = new Database(dbPath);
  
  try {
    // Check if role column already exists
    const tableInfo = db.prepare("PRAGMA table_info(users)").all();
    const hasRole = tableInfo.some(col => col.name === 'role');
    
    if (!hasRole) {
      logInfo('Adding role column to users table...');
      
      // Add new columns
      db.exec(`
        ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user' NOT NULL;
        ALTER TABLE users ADD COLUMN is_active INTEGER DEFAULT 1 NOT NULL;
        ALTER TABLE users ADD COLUMN last_login_at TEXT;
        ALTER TABLE users ADD COLUMN email_verified INTEGER DEFAULT 0 NOT NULL;
        ALTER TABLE users ADD COLUMN email_verified_at TEXT;
      `);
      
      // Create indexes
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
        CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
        CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
      `);
      
      logSuccess('User table updated with role-based fields');
    } else {
      logInfo('Role column already exists, skipping user table migration');
    }
    
    // Create admin user if not exists
    const adminExists = db.prepare("SELECT id FROM users WHERE email = ?").get('<EMAIL>');
    
    if (!adminExists) {
      logInfo('Creating default admin user...');
      const adminId = 'admin-' + Date.now();
      
      // Insert admin user
      db.prepare(`
        INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
        VALUES (?, ?, ?, 'admin', 1, 1, datetime('now'), datetime('now'))
      `).run(adminId, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
      
      // Create admin profile
      const profileId = 'profile-' + Date.now();
      db.prepare(`
        INSERT INTO profiles (id, user_id, nama_lengkap, created_at, updated_at)
        VALUES (?, ?, 'Administrator', datetime('now'), datetime('now'))
      `).run(profileId, adminId);
      
      logSuccess('Default admin user created');
      logInfo('  Email: <EMAIL>');
      logInfo('  Password: admin123');
    } else {
      logInfo('Admin user already exists');
    }
    
    // Update existing users to have default role
    const updated = db.prepare("UPDATE users SET role = 'user' WHERE role IS NULL OR role = ''").run();
    if (updated.changes > 0) {
      logInfo(`Updated ${updated.changes} existing users with default role`);
    }
    
    logSuccess('SQLite migration completed successfully');
    
  } catch (error) {
    throw new Error(`SQLite migration failed: ${error.message}`);
  } finally {
    db.close();
  }
}

async function runMySQLMigration(url) {
  logInfo('Running MySQL migration...');
  
  const connection = await mysql.createConnection(url);
  
  try {
    // Check if role column already exists
    const [columns] = await connection.execute("SHOW COLUMNS FROM users LIKE 'role'");
    
    if (columns.length === 0) {
      logInfo('Adding role column to users table...');
      
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN role VARCHAR(20) DEFAULT 'user' NOT NULL AFTER password,
        ADD COLUMN is_active BOOLEAN DEFAULT TRUE NOT NULL AFTER role,
        ADD COLUMN last_login_at TIMESTAMP NULL AFTER is_active,
        ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL AFTER last_login_at,
        ADD COLUMN email_verified_at TIMESTAMP NULL AFTER email_verified
      `);
      
      // Create indexes
      await connection.execute(`CREATE INDEX idx_users_role ON users(role)`);
      await connection.execute(`CREATE INDEX idx_users_active ON users(is_active)`);
      await connection.execute(`CREATE INDEX idx_users_email_verified ON users(email_verified)`);
      
      logSuccess('User table updated with role-based fields');
    } else {
      logInfo('Role column already exists, skipping user table migration');
    }
    
    // Create admin user if not exists
    const [adminExists] = await connection.execute("SELECT id FROM users WHERE email = ?", ['<EMAIL>']);
    
    if (adminExists.length === 0) {
      logInfo('Creating default admin user...');
      
      // Generate UUIDs
      const adminId = 'admin-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      const profileId = 'profile-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      
      // Insert admin user
      await connection.execute(`
        INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
        VALUES (?, ?, ?, 'admin', TRUE, TRUE, NOW(), NOW())
      `, [adminId, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi']);
      
      // Create admin profile
      await connection.execute(`
        INSERT INTO profiles (id, user_id, nama_lengkap, created_at, updated_at)
        VALUES (?, ?, 'Administrator', NOW(), NOW())
      `, [profileId, adminId]);
      
      logSuccess('Default admin user created');
      logInfo('  Email: <EMAIL>');
      logInfo('  Password: admin123');
    } else {
      logInfo('Admin user already exists');
    }
    
    // Update existing users to have default role
    const [result] = await connection.execute("UPDATE users SET role = 'user' WHERE role IS NULL OR role = ''");
    if (result.affectedRows > 0) {
      logInfo(`Updated ${result.affectedRows} existing users with default role`);
    }
    
    logSuccess('MySQL migration completed successfully');
    
  } catch (error) {
    throw new Error(`MySQL migration failed: ${error.message}`);
  } finally {
    await connection.end();
  }
}

async function runMigration() {
  logHeader('Database Migration - Adding Role-Based Access Control');
  
  try {
    const { provider, url } = await detectDatabaseProvider();
    logInfo(`Detected provider: ${provider}`);
    logInfo(`Database URL: ${url.replace(/:[^:@]+@/, ':****@')}`);
    
    if (provider === 'sqlite') {
      await runSQLiteMigration(url);
    } else if (provider === 'aiven-mysql' || provider === 'mysql') {
      await runMySQLMigration(url);
    } else if (provider === 'neon-postgresql') {
      logError('PostgreSQL migration not implemented yet');
      process.exit(1);
    } else {
      throw new Error(`Unsupported provider: ${provider}`);
    }
    
    logSuccess('🎉 Migration completed successfully!');
    logInfo('You can now use the admin dashboard with:');
    logInfo('  Email: <EMAIL>');
    logInfo('  Password: admin123');
    
  } catch (error) {
    logError(`Migration failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run migration
runMigration();
