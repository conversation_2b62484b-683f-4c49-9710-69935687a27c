import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import logoImage from "@assets/logo_tokenpedia_nobg_1755041851408.png";
import { Menu, X, Home, Brain, BookOpen, User, Wifi, WifiOff, RefreshCw, CheckCircle, Clock, Shield, Heart, Sparkles, UserPlus, Film, Download, Gamepad2 } from "lucide-react";
import { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [location] = useLocation();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Online/Offline status tracking
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="w-4 h-4" />;
    }
    return <CheckCircle className="w-4 h-4" />;
  };

  const getStatusColor = () => {
    if (!isOnline) {
      return 'bg-red-500 text-white';
    }
    return 'bg-emerald-500 text-white';
  };

  const navItems = [
    { path: "/", label: "Beranda", icon: Home, hash: "#home" },
    { path: "/#features", label: "Fitur", icon: Sparkles, hash: "#features" },
    { path: "/assessments", label: "Assessment", icon: Brain },
    { path: "/movies", label: "Film", icon: Film },
    { path: "/downloads", label: "Download", icon: Download },
    { path: "/games", label: "Game", icon: Gamepad2 },
    { path: "/education", label: "Edukasi", icon: Heart },
  ];

  const handleNavClick = (item: typeof navItems[0]) => {
    setIsOpen(false);
    if (item.hash) {
      if (location !== "/") {
        window.location.href = item.path;
      } else {
        document.querySelector(item.hash)?.scrollIntoView({ behavior: "smooth" });
      }
    }
  };

  return (
    <header className="w-full sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <nav className="container mx-auto flex items-center justify-between h-16 px-4">
        {/* Logo - Eye catching placement */}
        <Link href="/" className="flex items-center gap-3 hover:opacity-90 transition-opacity">
          <div className="relative">
            <img src={logoImage} alt="TokenPedia SantriMental Logo" className="h-12 w-12 rounded-full border-2 border-primary/20" />
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
          <span className="font-bold text-xl tracking-tight bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            SantriMental
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-6">
          {navItems.map((item) => (
            <Link key={item.path} href={item.path} className="group">
              <div 
                className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-primary/10 hover:text-primary cursor-pointer"
                onClick={() => handleNavClick(item)}
              >
                <item.icon size={16} />
                {item.label}
              </div>
            </Link>
          ))}
        </div>

        {/* Desktop Status & Auth Buttons */}
        <div className="hidden md:flex items-center gap-3">
          {/* Status Indicator */}
          <div className={`px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-2 ${getStatusColor()}`}>
            {getStatusIcon()}
            {isOnline ? 'Online' : 'Offline'}
          </div>

          {/* Auth Buttons */}
          <Link href="/login">
            <Button variant="outline" size="sm" className="gap-2 hover:bg-primary/10 transition-colors">
              <Shield size={16} />
              Login
            </Button>
          </Link>
          <Link href="/register">
            <Button variant="default" size="sm" className="bg-gradient-to-r from-primary to-emerald-600 hover:from-primary/90 hover:to-emerald-600/90 transition-all">
              <UserPlus size={16} className="mr-1" />
              Daftar
            </Button>
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          onClick={() => setIsOpen(!isOpen)}
          data-testid="mobile-menu-toggle"
        >
          {isOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </nav>

      {/* Mobile Menu */}
      {isOpen && (
        <div className="md:hidden border-t bg-background/95 backdrop-blur">
          <div className="container mx-auto px-4 py-4 space-y-2">
            {navItems.map((item) => (
              <Link key={item.path} href={item.path}>
                <div 
                  className="flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors hover:bg-primary/10 hover:text-primary cursor-pointer"
                  onClick={() => handleNavClick(item)}
                  data-testid={`mobile-nav-${item.label.toLowerCase()}`}
                >
                  <item.icon size={18} />
                  {item.label}
                </div>
              </Link>
            ))}
            <div className="pt-4 border-t space-y-3">
              {/* Mobile Status Indicator */}
              <div className={`px-3 py-2 rounded-full text-sm font-medium flex items-center justify-center gap-2 ${getStatusColor()}`}>
                {getStatusIcon()}
                {isOnline ? 'Online' : 'Offline'}
              </div>

              {/* Mobile Auth Buttons */}
              <div className="flex flex-col gap-2">
                <Link href="/login">
                  <Button variant="outline" size="sm" className="gap-2 w-full hover:bg-primary/10 transition-colors" data-testid="mobile-login">
                    <Shield size={16} />
                    Login
                  </Button>
                </Link>
                <Link href="/register">
                  <Button variant="default" size="sm" className="w-full bg-gradient-to-r from-primary to-emerald-600 hover:from-primary/90 hover:to-emerald-600/90 transition-all" data-testid="mobile-register">
                    <UserPlus size={16} className="mr-1" />
                    Daftar
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
