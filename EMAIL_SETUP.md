# Email Setup Guide - SantriMental

## 📧 Konfigurasi Email untuk Testing

**Status**: ✅ Email service sudah dioptimasi untuk graceful handling

### 1. Setup Gmail SMTP (Opsional)

#### **Langkah Detail Setup Gmail App Password**:

1. **<PERSON><PERSON><PERSON> ke Google Account Settings**:
   - <PERSON><PERSON> [myaccount.google.com](https://myaccount.google.com)
   - Klik **Security** di sidebar kiri

2. **Enable 2-Step Verification** (jika belum aktif):
   - Scroll ke "Signing in to Google"
   - Klik "2-Step Verification"
   - I<PERSON>ti petunjuk untuk mengaktifkan

3. **Generate App Password**:
   - <PERSON><PERSON><PERSON> di halaman Security
   - Scroll ke "Signing in to Google"
   - Klik "App passwords"
   - Pilih "Mail" sebagai app type
   - Pilih device (contoh: "Windows Computer")
   - Klik "Generate"
   - **COPY 16-digit password** yang muncul (format: xxxx xxxx xxxx xxxx)

4. **Update file `.env`**:
   ```env
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT=587
   SMTP_USER="<EMAIL>"
   SMTP_PASS="your-16-digit-app-password"
   ```

   **Contoh**:
   ```env
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT=587
   SMTP_USER="<EMAIL>"
   SMTP_PASS="abcd efgh ijkl mnop"
   ```

### 2. Testing Tanpa Email Setup (Default)

**Status Saat Ini**: ✅ Email service berjalan dalam mode graceful
- Jika kredensial SMTP belum dikonfigurasi, email akan di-skip
- Console akan menampilkan log: `⚠️ Email skipped - SMTP credentials not configured`
- Registrasi tetap berhasil tanpa mengirim email
- Tidak ada error yang mengganggu user experience

### 3. Testing Email Delivery (Setelah Setup)

**Target Email untuk Testing**: `<EMAIL>`

#### Test Scenario:
1. Buka aplikasi di `http://localhost:5000`
2. Klik tombol "Daftar" di navigation
3. Isi form pendaftaran:
   - Email: `<EMAIL>`
   - Password: `password123`
   - Konfirmasi Password: `password123`
4. Klik "Daftar"
5. Cek email di inbox `<EMAIL>`

#### Expected Result:
- ✅ User berhasil terdaftar
- ✅ Email welcome terkirim ke `<EMAIL>`
- ✅ Email berisi template yang menarik dengan branding SantriMental
- ✅ Link untuk login ke aplikasi

### 3. Email Template Features

Email yang dikirim memiliki fitur:
- 🎨 **Design Responsif**: Template HTML yang menarik
- 🏷️ **Branding**: Logo dan warna SantriMental
- 📱 **Mobile-Friendly**: Tampil baik di semua device
- 🔗 **Call-to-Action**: Button untuk mulai menggunakan aplikasi
- 🕌 **Islamic Touch**: Salam dan doa dalam bahasa Arab

### 4. Troubleshooting

#### Error: "Invalid login: 535-5.7.8 Username and Password not accepted"
**Solusi**:
1. Pastikan 2-Step Verification aktif di Gmail
2. Generate App Password baru
3. Gunakan App Password (16 digit) bukan password Gmail biasa
4. Update SMTP_PASS di file .env

#### Error: "Connection timeout"
**Solusi**:
1. Cek koneksi internet
2. Pastikan port 587 tidak diblokir firewall
3. Coba gunakan port 465 dengan secure: true

#### Email tidak terkirim tapi tidak ada error
**Solusi**:
1. Cek spam folder
2. Pastikan email recipient valid
3. Cek log server untuk detail error

### 5. Production Setup

Untuk production, disarankan menggunakan:
- **SendGrid**: Service email yang reliable
- **Amazon SES**: Cost-effective untuk volume besar
- **Mailgun**: Developer-friendly API

Example untuk SendGrid:
```env
SMTP_HOST="smtp.sendgrid.net"
SMTP_PORT=587
SMTP_USER="apikey"
SMTP_PASS="your-sendgrid-api-key"
```

### 6. Email Content Customization

Template email dapat dikustomisasi di file `server/email-service.ts`:

```typescript
export function generateWelcomeEmail(email: string): string {
  // Customize HTML template here
  return `<!DOCTYPE html>...`;
}
```

**Fitur yang bisa ditambahkan**:
- Email verification link
- Password reset functionality
- Newsletter subscription
- Assessment reminders

### 7. Monitoring & Analytics

Untuk production, tambahkan:
- Email delivery tracking
- Open rate monitoring
- Click-through rate analytics
- Bounce rate handling

---

## 🧪 Quick Test Command

Untuk test cepat email service:

```bash
# 1. Update .env dengan kredensial Gmail yang valid
# 2. Restart server
npm run dev

# 3. Test registration dengan email target
curl -X POST http://localhost:5000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

**Expected Response**:
```json
{
  "user": {
    "id": "uuid-here",
    "email": "<EMAIL>"
  },
  "access_token": "jwt-token-here"
}
```

Dan email welcome akan terkirim ke `<EMAIL>` 📧✨
