import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Play, Search, Clock, Eye, ThumbsUp, X } from 'lucide-react';
import Navbar from '@/components/sections/Navbar';
import Footer from '@/components/sections/Footer';

interface Video {
  id: string;
  title: string;
  youtubeId: string;
  thumbnail: string;
  duration: string;
  views: string;
  description: string;
  category: string;
}

const videos: Video[] = [
  {
    id: '1',
    title: 'Seri 3 Manajemen Stres Dalam Mencegah Pernikahan Muda Melalui Budaya Lokal',
    youtubeId: '_22feazq014',
    thumbnail: 'https://img.youtube.com/vi/_22feazq014/maxresdefault.jpg',
    duration: '15:30',
    views: '2.1K',
    description: 'Video edukasi tentang manajemen stres untuk mencegah pernikahan muda dengan pendekatan budaya lokal.',
    category: 'Manajemen Stres'
  },
  {
    id: '2',
    title: 'Seri 1 Kesakralan dan Filosofi Pernikahan',
    youtubeId: 'rGYAoVZxau4',
    thumbnail: 'https://img.youtube.com/vi/rGYAoVZxau4/maxresdefault.jpg',
    duration: '18:45',
    views: '3.5K',
    description: 'Memahami kesakralan dan filosofi pernikahan dalam perspektif Islam dan budaya.',
    category: 'Filosofi'
  },
  {
    id: '3',
    title: 'Eps 3 Dokter Medis',
    youtubeId: 'GCN6d7A-yyk',
    thumbnail: 'https://img.youtube.com/vi/GCN6d7A-yyk/maxresdefault.jpg',
    duration: '12:20',
    views: '1.8K',
    description: 'Konsultasi dengan dokter medis tentang kesehatan mental santri.',
    category: 'Konsultasi Medis'
  },
  {
    id: '4',
    title: 'Seri 4 Manajemen Beban Dalam Pencegahan Pernikahan Muda Melalui Budaya Lokal',
    youtubeId: 'NCIZJWA-_Cw',
    thumbnail: 'https://img.youtube.com/vi/NCIZJWA-_Cw/maxresdefault.jpg',
    duration: '16:15',
    views: '2.7K',
    description: 'Strategi manajemen beban untuk pencegahan pernikahan muda dengan pendekatan budaya lokal.',
    category: 'Manajemen Beban'
  },
  {
    id: '5',
    title: 'Eps 2 Orang Tua Episode 2',
    youtubeId: 'btSo9R8vGH0',
    thumbnail: 'https://img.youtube.com/vi/btSo9R8vGH0/maxresdefault.jpg',
    duration: '14:30',
    views: '4.2K',
    description: 'Peran orang tua dalam mendukung kesehatan mental anak di pesantren.',
    category: 'Peran Orang Tua'
  },
  {
    id: '6',
    title: 'Cara Santri Cari Bantuan Kesehatan Jiwa',
    youtubeId: 'X9tNDlCdttw',
    thumbnail: 'https://img.youtube.com/vi/X9tNDlCdttw/maxresdefault.jpg',
    duration: '11:45',
    views: '5.1K',
    description: 'Panduan praktis bagi santri untuk mencari bantuan kesehatan jiwa yang tepat.',
    category: 'Panduan Bantuan'
  }
];

export default function Movies() {
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredVideos = videos.filter(video =>
    video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent mb-4">
            🎬 Video Edukasi Kesehatan Mental Santri
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Koleksi video edukasi kesehatan mental khusus untuk santri pesantren dengan pendekatan Islami
          </p>
        </div>

        {/* Search */}
        <div className="relative max-w-md mx-auto mb-8">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
          <Input
            placeholder="Cari video edukasi..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Video Player */}
        {selectedVideo && (
          <Card className="mb-8">
            <CardContent className="p-0">
              <div className="relative">
                <div className="aspect-video">
                  <iframe
                    width="100%"
                    height="100%"
                    src={`https://www.youtube.com/embed/${selectedVideo.youtubeId}?autoplay=1`}
                    title={selectedVideo.title}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="rounded-t-lg"
                  ></iframe>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedVideo(null)}
                  className="absolute top-4 right-4 bg-black/50 text-white border-white/20 hover:bg-black/70"
                >
                  <X size={16} />
                </Button>
              </div>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <Badge variant="secondary">{selectedVideo.category}</Badge>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Eye size={16} />
                      {selectedVideo.views} views
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock size={16} />
                      {selectedVideo.duration}
                    </div>
                  </div>
                </div>
                <h2 className="text-2xl font-bold mb-2">{selectedVideo.title}</h2>
                <p className="text-muted-foreground">{selectedVideo.description}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredVideos.map((video) => (
            <Card key={video.id} className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
              <div className="relative overflow-hidden">
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className="w-full aspect-video object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button
                    size="lg"
                    onClick={() => setSelectedVideo(video)}
                    className="gap-2 bg-white/20 backdrop-blur-sm border-white/20 text-white hover:bg-white/30"
                  >
                    <Play size={20} />
                    Putar Video
                  </Button>
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 rounded text-sm">
                  {video.duration}
                </div>
                <div className="absolute top-2 left-2">
                  <Badge className="bg-primary/90 text-white">
                    {video.category}
                  </Badge>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-lg line-clamp-2 group-hover:text-primary transition-colors">
                  {video.title}
                </CardTitle>
                <CardDescription className="line-clamp-3">
                  {video.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Eye size={16} />
                    {video.views} views
                  </div>
                  <div className="flex items-center gap-1">
                    <ThumbsUp size={16} />
                    Suka
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredVideos.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎬</div>
            <h3 className="text-xl font-semibold mb-2">Tidak ada video ditemukan</h3>
            <p className="text-muted-foreground">
              Coba ubah kata kunci pencarian
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
