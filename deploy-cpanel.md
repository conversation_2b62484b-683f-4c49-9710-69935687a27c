# 🚀 Panduan Deployment SantriMental ke cPanel Shared Hosting

## 📋 Persiapan Deployment

### 1. **Persyaratan cPanel**
- ✅ Node.js support (minimal v18)
- ✅ MySQL database
- ✅ SSL certificate (untuk PWA)
- ✅ File manager access
- ✅ Subdomain/domain setup

### 2. **Build Production**
```bash
# Install dependencies
npm install

# Build aplikasi
npm run build

# Hasil build akan ada di folder dist/
```

### 3. **Struktur File untuk Upload**
```
public_html/
├── dist/
│   ├── public/          # Frontend files
│   └── index.js         # Server file
├── node_modules/        # Dependencies
├── package.json
├── .env                 # Environment variables
└── data/               # SQLite database folder
```

## 🗄️ Setup Database MySQL

### 1. **Buat Database di cPanel**
```sql
-- Nama database: santrimental_prod
CREATE DATABASE santrimental_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. **Import Schema**
```bash
# Upload file database/schema.sql ke cPanel
# Import melalui phpMyAdmin atau MySQL command line
mysql -u username -p santrimental_prod < schema.sql
```

### 3. **Konfigurasi Environment**
```env
# .env file untuk production
NODE_ENV=production
DATABASE_URL="mysql://username:password@localhost:3306/santrimental_prod"
PORT=3000
ENABLE_OFFLINE=true
```

## 📁 Upload Files ke cPanel

### 1. **Compress dan Upload**
```bash
# Compress semua file kecuali node_modules
tar -czf santrimental.tar.gz --exclude=node_modules .

# Upload santrimental.tar.gz ke cPanel File Manager
# Extract di public_html atau subdomain folder
```

### 2. **Install Dependencies di Server**
```bash
# Masuk ke Terminal cPanel atau SSH
cd public_html
npm install --production
```

### 3. **Setup Node.js App di cPanel**
- App Root: `/public_html`
- Startup File: `dist/index.js`
- Node.js Version: 18.x atau lebih baru

## 🔧 Konfigurasi cPanel

### 1. **Node.js App Setup**
```javascript
// App Root: /public_html
// Startup File: dist/index.js
// Environment Variables:
NODE_ENV=production
DATABASE_URL=mysql://user:pass@localhost:3306/santrimental_prod
PORT=3000
ENABLE_OFFLINE=true
```

### 2. **SSL Certificate**
- Aktifkan SSL untuk domain/subdomain
- Redirect HTTP ke HTTPS (untuk PWA)

### 3. **File Permissions**
```bash
chmod 755 public_html
chmod 644 public_html/.env
chmod 755 public_html/data
chmod 666 public_html/data/santrimental.db
```

## 🌐 Domain & Subdomain Setup

### 1. **Subdomain (Recommended)**
```
subdomain: app.yourdomain.com
Document Root: /public_html/santrimental
```

### 2. **Main Domain**
```
domain: yourdomain.com
Document Root: /public_html
```

## 🔄 Fitur Offline & Sync

### 1. **SQLite untuk Offline**
- Data disimpan di `data/santrimental.db`
- Auto-sync ke MySQL saat online
- Background sync setiap 30 detik

### 2. **PWA Features**
- ✅ Service Worker untuk caching
- ✅ Offline page
- ✅ App manifest
- ✅ Install prompt
- ✅ Background sync

### 3. **Sync Indicator**
- Real-time status koneksi
- Jumlah data pending sync
- Manual sync trigger
- Last sync timestamp

## 🚀 Start Application

### 1. **Manual Start**
```bash
cd public_html
node dist/index.js
```

### 2. **Auto Start (cPanel)**
- Gunakan Node.js App manager di cPanel
- Set startup file: `dist/index.js`
- Enable auto-restart

### 3. **Process Manager (PM2)**
```bash
# Install PM2 globally
npm install -g pm2

# Start app dengan PM2
pm2 start dist/index.js --name "santrimental"

# Save PM2 configuration
pm2 save
pm2 startup
```

## 📱 Testing Mobile & Offline

### 1. **PWA Installation**
- Buka di mobile browser
- Tap "Add to Home Screen"
- Test offline functionality

### 2. **Sync Testing**
- Matikan internet
- Isi assessment
- Nyalakan internet
- Cek sync indicator

### 3. **Performance Testing**
- Lighthouse audit
- Mobile responsiveness
- Offline capabilities

## 🔍 Monitoring & Logs

### 1. **Application Logs**
```bash
# View logs
tail -f logs/app.log

# PM2 logs
pm2 logs santrimental
```

### 2. **Database Monitoring**
```sql
-- Check sync queue
SELECT COUNT(*) FROM sync_queue;

-- Check last sync times
SELECT table_name, MAX(last_sync) FROM (
  SELECT 'users' as table_name, last_sync FROM users WHERE synced = 1
  UNION ALL
  SELECT 'profiles', last_sync FROM profiles WHERE synced = 1
) t GROUP BY table_name;
```

### 3. **Performance Monitoring**
- Monitor memory usage
- Check database connections
- Monitor sync queue size

## 🛠️ Troubleshooting

### 1. **Common Issues**
```bash
# Node.js version issues
node --version  # Should be 18+

# Permission issues
chmod -R 755 public_html
chown -R username:username public_html

# Database connection
mysql -u username -p -e "SELECT 1"
```

### 2. **Sync Issues**
```javascript
// Check sync status
GET /api/sync/status

// Manual sync trigger
POST /api/sync/trigger
```

### 3. **PWA Issues**
- Check HTTPS certificate
- Verify manifest.json
- Check service worker registration

## 📊 Production Checklist

- [ ] Database created and schema imported
- [ ] Environment variables configured
- [ ] SSL certificate installed
- [ ] Node.js app configured in cPanel
- [ ] File permissions set correctly
- [ ] PWA manifest accessible
- [ ] Service worker registered
- [ ] Offline functionality tested
- [ ] Sync mechanism working
- [ ] Mobile responsiveness verified
- [ ] Performance optimized

## 🎯 Post-Deployment

### 1. **User Training**
- Cara install PWA
- Penggunaan offline
- Sync indicator explanation

### 2. **Maintenance**
- Regular database backup
- Monitor sync queue
- Update dependencies
- Performance monitoring

### 3. **Updates**
```bash
# Update process
npm run build
# Upload new dist/ folder
# Restart Node.js app
```

---

**🎉 SantriMental siap digunakan secara offline-first dengan auto-sync ke server!**
