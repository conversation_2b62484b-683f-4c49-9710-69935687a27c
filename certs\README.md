# 📜 SSL Certificates Directory

## 🔒 Security Notice
This directory contains SSL certificates for database connections. **Never commit these files to version control!**

## 📁 Expected Files

### AIVEN MySQL Certificate
- **File**: `aiven-ca.pem`
- **Source**: Download from AIVEN Console
- **Usage**: SSL connection to AIVEN MySQL service

## 📋 Setup Instructions

### 1. Download AIVEN Certificate
1. Go to your AIVEN Console
2. Select your MySQL service: `santrimental-mysql`
3. Go to "Connection information" tab
4. Download the CA certificate
5. Save it as `aiven-ca.pem` in this directory

### 2. Verify File
```bash
# Check if certificate file exists
ls -la certs/aiven-ca.pem

# Verify certificate content (should start with -----BEGIN CERTIFICATE-----)
head -n 5 certs/aiven-ca.pem
```

### 3. Environment Configuration
Add to your `.env` file:
```env
AIVEN_CA_CERT_PATH="./certs/aiven-ca.pem"
```

## 🛡️ Security Best Practices

### File Permissions
```bash
# Set secure permissions (Linux/Mac)
chmod 600 certs/*.pem
```

### Git Protection
- ✅ Directory is already in `.gitignore`
- ✅ Certificate files will not be committed
- ✅ Safe to store locally

### Production Deployment
- Use environment variables for certificate content
- Or secure file storage service
- Never expose certificates in logs

## 🔍 Troubleshooting

### Certificate Not Found
```
Error: ENOENT: no such file or directory, open './certs/aiven-ca.pem'
```
**Solution**: Download certificate from AIVEN and save as `aiven-ca.pem`

### Invalid Certificate Format
```
Error: unable to verify the first certificate
```
**Solution**: Ensure certificate starts with `-----BEGIN CERTIFICATE-----`

### Permission Denied
```
Error: EACCES: permission denied, open './certs/aiven-ca.pem'
```
**Solution**: Check file permissions with `chmod 644 certs/aiven-ca.pem`

## 📞 Support

If you need help with certificate setup:
1. Check AIVEN documentation
2. Verify file exists and has correct format
3. Check application logs for detailed error messages
4. Use database dashboard at `/database` for status

---

**🔐 Keep your certificates secure and never share them publicly!**
