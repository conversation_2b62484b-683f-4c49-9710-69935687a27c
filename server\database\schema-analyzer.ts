/**
 * 🔍 Database Schema Analyzer
 * 
 * Analyzes current database schema and compares with target schemas
 */

import { ConnectionManager } from './connection-manager';
import fs from 'fs';
import path from 'path';

export interface TableInfo {
  name: string;
  columns: ColumnInfo[];
  indexes: IndexInfo[];
  foreignKeys: ForeignKeyInfo[];
}

export interface ColumnInfo {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: string;
  isPrimaryKey: boolean;
  isUnique: boolean;
  maxLength?: number;
}

export interface IndexInfo {
  name: string;
  columns: string[];
  isUnique: boolean;
}

export interface ForeignKeyInfo {
  name: string;
  column: string;
  referencedTable: string;
  referencedColumn: string;
  onDelete?: string;
  onUpdate?: string;
}

export interface SchemaComparison {
  missingTables: string[];
  extraTables: string[];
  tableDifferences: {
    [tableName: string]: {
      missingColumns: string[];
      extraColumns: string[];
      columnDifferences: {
        [columnName: string]: {
          current: ColumnInfo;
          expected: ColumnInfo;
        };
      };
    };
  };
}

export class SchemaAnalyzer {
  private connectionManager: ConnectionManager;

  constructor() {
    this.connectionManager = ConnectionManager.getInstance();
  }

  /**
   * Get current database schema information
   */
  async getCurrentSchema(): Promise<TableInfo[]> {
    const connection = await this.connectionManager.getConnection();
    const provider = this.connectionManager.getCurrentProvider();

    switch (provider) {
      case 'mysql':
      case 'aiven-mysql':
        return this.getMySQLSchema(connection);
      case 'neon-postgresql':
        return this.getPostgreSQLSchema(connection);
      case 'sqlite':
        return this.getSQLiteSchema(connection);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Get MySQL schema information
   */
  private async getMySQLSchema(connection: any): Promise<TableInfo[]> {
    const tables: TableInfo[] = [];
    
    try {
      // Get all tables
      const [tableRows] = await connection.getPool().execute(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_TYPE = 'BASE TABLE'
      `);

      for (const tableRow of tableRows as any[]) {
        const tableName = tableRow.TABLE_NAME;
        
        // Get columns
        const [columnRows] = await connection.getPool().execute(`
          SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            COLUMN_KEY,
            CHARACTER_MAXIMUM_LENGTH,
            EXTRA
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = ?
          ORDER BY ORDINAL_POSITION
        `, [tableName]);

        const columns: ColumnInfo[] = (columnRows as any[]).map(col => ({
          name: col.COLUMN_NAME,
          type: col.DATA_TYPE,
          nullable: col.IS_NULLABLE === 'YES',
          defaultValue: col.COLUMN_DEFAULT,
          isPrimaryKey: col.COLUMN_KEY === 'PRI',
          isUnique: col.COLUMN_KEY === 'UNI',
          maxLength: col.CHARACTER_MAXIMUM_LENGTH
        }));

        // Get indexes
        const [indexRows] = await connection.getPool().execute(`
          SELECT 
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE
          FROM INFORMATION_SCHEMA.STATISTICS 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = ?
          ORDER BY INDEX_NAME, SEQ_IN_INDEX
        `, [tableName]);

        const indexMap = new Map<string, IndexInfo>();
        for (const indexRow of indexRows as any[]) {
          const indexName = indexRow.INDEX_NAME;
          if (!indexMap.has(indexName)) {
            indexMap.set(indexName, {
              name: indexName,
              columns: [],
              isUnique: indexRow.NON_UNIQUE === 0
            });
          }
          indexMap.get(indexName)!.columns.push(indexRow.COLUMN_NAME);
        }

        // Get foreign keys
        const [fkRows] = await connection.getPool().execute(`
          SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME,
            DELETE_RULE,
            UPDATE_RULE
          FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
          JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
            ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
          WHERE kcu.TABLE_SCHEMA = DATABASE() 
          AND kcu.TABLE_NAME = ?
          AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `, [tableName]);

        const foreignKeys: ForeignKeyInfo[] = (fkRows as any[]).map(fk => ({
          name: fk.CONSTRAINT_NAME,
          column: fk.COLUMN_NAME,
          referencedTable: fk.REFERENCED_TABLE_NAME,
          referencedColumn: fk.REFERENCED_COLUMN_NAME,
          onDelete: fk.DELETE_RULE,
          onUpdate: fk.UPDATE_RULE
        }));

        tables.push({
          name: tableName,
          columns,
          indexes: Array.from(indexMap.values()),
          foreignKeys
        });
      }

      return tables;
    } catch (error) {
      console.error('Error getting MySQL schema:', error);
      throw error;
    }
  }

  /**
   * Get PostgreSQL schema information
   */
  private async getPostgreSQLSchema(connection: any): Promise<TableInfo[]> {
    // Implementation for PostgreSQL schema analysis
    // Similar to MySQL but with PostgreSQL-specific queries
    return [];
  }

  /**
   * Get SQLite schema information
   */
  private async getSQLiteSchema(connection: any): Promise<TableInfo[]> {
    const tables: TableInfo[] = [];
    
    try {
      // Get all tables
      const tableRows = connection.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all();

      for (const tableRow of tableRows) {
        const tableName = tableRow.name;
        
        // Get table info
        const columnRows = connection.db.prepare(`PRAGMA table_info(${tableName})`).all();
        
        const columns: ColumnInfo[] = columnRows.map((col: any) => ({
          name: col.name,
          type: col.type,
          nullable: !col.notnull,
          defaultValue: col.dflt_value,
          isPrimaryKey: col.pk === 1,
          isUnique: false, // Will be determined from indexes
          maxLength: undefined
        }));

        // Get indexes
        const indexRows = connection.db.prepare(`PRAGMA index_list(${tableName})`).all();
        const indexes: IndexInfo[] = [];
        
        for (const indexRow of indexRows) {
          const indexInfo = connection.db.prepare(`PRAGMA index_info(${indexRow.name})`).all();
          indexes.push({
            name: indexRow.name,
            columns: indexInfo.map((info: any) => info.name),
            isUnique: indexRow.unique === 1
          });
        }

        // Get foreign keys
        const fkRows = connection.db.prepare(`PRAGMA foreign_key_list(${tableName})`).all();
        const foreignKeys: ForeignKeyInfo[] = fkRows.map((fk: any) => ({
          name: `fk_${tableName}_${fk.from}`,
          column: fk.from,
          referencedTable: fk.table,
          referencedColumn: fk.to,
          onDelete: fk.on_delete,
          onUpdate: fk.on_update
        }));

        tables.push({
          name: tableName,
          columns,
          indexes,
          foreignKeys
        });
      }

      return tables;
    } catch (error) {
      console.error('Error getting SQLite schema:', error);
      throw error;
    }
  }

  /**
   * Compare current schema with expected schema
   */
  async compareSchemas(expectedTables: string[]): Promise<SchemaComparison> {
    const currentSchema = await this.getCurrentSchema();
    const currentTableNames = currentSchema.map(t => t.name);
    
    return {
      missingTables: expectedTables.filter(name => !currentTableNames.includes(name)),
      extraTables: currentTableNames.filter(name => !expectedTables.includes(name)),
      tableDifferences: {}
    };
  }

  /**
   * Get expected tables from schema definition
   */
  getExpectedTables(): string[] {
    return [
      'users',
      'profiles',
      'assessment_configs',
      'assessment_sessions',
      'assessment_responses',
      'assessment_results',
      'user_progress',
      'crisis_alerts',
      'counselor_assignments',
      'educational_modules',
      'user_learning_progress',
      'audit_logs'
    ];
  }

  /**
   * Generate schema report
   */
  async generateSchemaReport(): Promise<string> {
    const currentSchema = await this.getCurrentSchema();
    const expectedTables = this.getExpectedTables();
    const comparison = await this.compareSchemas(expectedTables);
    
    let report = `# Database Schema Report\n\n`;
    report += `**Provider**: ${this.connectionManager.getCurrentProvider()}\n`;
    report += `**Generated**: ${new Date().toISOString()}\n\n`;
    
    report += `## Current Tables (${currentSchema.length})\n`;
    for (const table of currentSchema) {
      report += `- **${table.name}** (${table.columns.length} columns, ${table.indexes.length} indexes)\n`;
    }
    
    if (comparison.missingTables.length > 0) {
      report += `\n## Missing Tables (${comparison.missingTables.length})\n`;
      for (const table of comparison.missingTables) {
        report += `- ${table}\n`;
      }
    }
    
    if (comparison.extraTables.length > 0) {
      report += `\n## Extra Tables (${comparison.extraTables.length})\n`;
      for (const table of comparison.extraTables) {
        report += `- ${table}\n`;
      }
    }
    
    return report;
  }
}
