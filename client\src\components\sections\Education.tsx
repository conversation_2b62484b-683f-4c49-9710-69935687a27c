import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import MediaLightbox, { MediaKind } from "@/components/media/MediaLightbox";
import { useState } from "react";

const Education = () => {
  const items: { title: string; link: string; kind: MediaKind }[] = [
    {
      title: "E-Modul Psikoedukasi <PERSON>",
      link: "https://drive.google.com/file/d/1J9Me3jFdIvwWZFfCzgyoCrHbEk2/view?usp=sharing",
      kind: "pdf",
    },
    {
      title: "Modul Perawatan Diri Kesehatan Jiwa di Pesantren",
      link: "https://drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W/view?usp=sharing",
      kind: "pdf",
    },
    {
      title: "Game Online Pencegahan Bullying - GEN ZAS",
      link: "https://itch.io/embed/2923060",
      kind: "iframe",
    },
    {
      title: "Film Psikoedukasi – Sesi 1",
      link: "https://drive.google.com/file/d/1iRiig-oZncFO20XThcdQ_r588YfsAIXe/view?usp=sharing",
      kind: "video",
    },
    {
      title: "Film Psikoedukasi – Sesi 2",
      link: "https://drive.google.com/file/d/1avTWpPYSWu6Ldu0pQKhk5ML5TLA6jDaR/view?usp=sharing",
      kind: "video",
    },
  ];

  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <section id="education" className="container mx-auto px-6 py-12 md:py-16">
      <h2 className="text-2xl md:text-3xl font-semibold">Edukasi & Promosi Kesehatan</h2>
      <p className="text-muted-foreground mt-2 max-w-prose">
        Materi pendukung untuk meningkatkan literasi kesehatan jiwa.
      </p>
      <div className="mt-8 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {items.map((it, idx) => (
          <Card key={it.title} className="h-full flex flex-col">
            <CardHeader>
              <CardTitle className="text-lg">{it.title}</CardTitle>
            </CardHeader>
            <CardContent className="mt-auto">
              <MediaLightbox
                title={it.title}
                url={it.link}
                kind={it.kind}
                open={openIndex === idx}
                onOpenChange={(o) => setOpenIndex(o ? idx : null)}
                trigger={
                  <Button onClick={() => setOpenIndex(idx)} variant="hero">
                    Buka
                  </Button>
                }
              />
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default Education;

