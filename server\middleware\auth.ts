/**
 * 🔐 Authentication & Authorization Middleware
 * 
 * Handles JWT authentication and role-based access control
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { ConnectionManager } from '../database/connection-manager.js';
import { eq, and } from 'drizzle-orm';
import { users, userPermissions } from '../../shared/schema.js';

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: string;
  isActive: boolean;
  emailVerified: boolean;
  permissions?: string[];
}

export interface AuthenticatedRequest extends Request {
  user?: AuthenticatedUser;
}

/**
 * JWT Authentication Middleware
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    console.log('🔍 Auth middleware - Header:', authHeader);
    console.log('🔍 Auth middleware - Token:', token ? token.substring(0, 20) + '...' : 'none');

    if (!token) {
      console.log('❌ Auth middleware - No token provided');
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET not configured');
      return res.status(500).json({
        success: false,
        error: 'Server configuration error'
      });
    }

    // Verify JWT token
    console.log('🔍 Auth middleware - Verifying token with secret:', jwtSecret ? 'exists' : 'missing');
    const decoded = jwt.verify(token, jwtSecret) as any;
    console.log('🔍 Auth middleware - Token decoded:', decoded);
    
    // Get user from database
    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();

    let user;
    if (connectionManager.getCurrentProvider() === 'sqlite') {
      const stmt = connection.db.prepare(`
        SELECT id, username, email, password_hash, first_name, role, email_verified_at
        FROM users
        WHERE id = ?
      `);
      user = stmt.get(decoded.userId);
    } else {
      const result = await connection.getPool().execute(
        'SELECT id, username, email, password_hash, first_name, role, email_verified_at FROM users WHERE id = ?',
        [decoded.userId]
      );
      user = (result as any)[0][0];
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token or user not found'
      });
    }

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      isActive: true, // All users in database are active
      emailVerified: Boolean(user.email_verified_at)
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  }
};

/**
 * Role-based Authorization Middleware
 */
export const requireRole = (allowedRoles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
    }

    next();
  };
};

/**
 * Admin-only Authorization Middleware
 */
export const requireAdmin = requireRole(['admin']);

/**
 * Permission-based Authorization Middleware
 */
export const requirePermission = (permission: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Admin users have all permissions
    if (req.user.role === 'admin') {
      return next();
    }

    try {
      // Check specific permission
      const connectionManager = ConnectionManager.getInstance();
      const connection = await connectionManager.getConnection();
      
      let hasPermission = false;
      
      if (connectionManager.getCurrentProvider() === 'sqlite') {
        const stmt = connection.db.prepare(`
          SELECT COUNT(*) as count 
          FROM user_permissions 
          WHERE user_id = ? AND permission = ? AND is_active = 1
          AND (expires_at IS NULL OR expires_at > datetime('now'))
        `);
        const result = stmt.get(req.user.id, permission);
        hasPermission = result.count > 0;
      } else {
        const result = await connection.getPool().execute(`
          SELECT COUNT(*) as count 
          FROM user_permissions 
          WHERE user_id = ? AND permission = ? AND is_active = 1
          AND (expires_at IS NULL OR expires_at > NOW())
        `, [req.user.id, permission]);
        hasPermission = (result as any)[0][0].count > 0;
      }

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: `Permission required: ${permission}`
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        error: 'Permission check failed'
      });
    }
  };
};

/**
 * Get user permissions
 */
export const getUserPermissions = async (userId: string): Promise<string[]> => {
  try {
    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();

    let user: any;

    if (connectionManager.getCurrentProvider() === 'sqlite') {
      const stmt = connection.db.prepare('SELECT role FROM users WHERE id = ?');
      user = stmt.get(userId);
    } else {
      const result = await connection.getPool().execute('SELECT role FROM users WHERE id = ?', [userId]);
      user = (result as any)[0][0];
    }

    if (!user) return [];

    // Return permissions based on role
    const rolePermissions: Record<string, string[]> = {
      'admin': ['admin:read', 'admin:write', 'user:read', 'user:write'],
      'moderator': ['user:read', 'user:write'],
      'user': ['user:read']
    };

    return rolePermissions[user.role] || [];
  } catch (error) {
    console.error('Error getting user permissions:', error);
    return [];
  }
};

/**
 * Generate JWT token
 */
export const generateToken = (user: { id: string; email: string; role: string }): string => {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT_SECRET not configured');
  }

  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role
    },
    jwtSecret,
    { expiresIn: '24h' }
  );
};

/**
 * Hash password using bcrypt
 */
export const hashPassword = async (password: string): Promise<string> => {
  const bcrypt = await import('bcrypt');
  return bcrypt.hash(password, 10);
};

/**
 * Compare password with hash
 */
export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  const bcrypt = await import('bcrypt');
  return bcrypt.compare(password, hash);
};
