#!/usr/bin/env node

/**
 * 📊 Database Status Dashboard
 * 
 * Real-time status monitoring for SantriMental database system
 */

import axios from 'axios';
import fs from 'fs';

const BASE_URL = 'http://localhost:5000';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function clearScreen() {
  console.clear();
}

function logHeader(message) {
  log('═'.repeat(80), 'cyan');
  log(`🗄️  ${message}`, 'cyan');
  log('═'.repeat(80), 'cyan');
}

function logSection(message) {
  log(`\n📋 ${message}`, 'yellow');
  log('─'.repeat(60), 'yellow');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Get server status
async function getServerStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    return {
      status: 'online',
      data: response.data,
      responseTime: response.headers['x-response-time'] || 'N/A'
    };
  } catch (error) {
    return {
      status: 'offline',
      error: error.message,
      responseTime: 'N/A'
    };
  }
}

// Get database configuration
async function getDatabaseConfig() {
  try {
    const response = await axios.get(`${BASE_URL}/api/database/config`, { timeout: 5000 });
    return {
      status: 'success',
      data: response.data.data
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    };
  }
}

// Get database status (if available)
async function getDatabaseStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/api/database/status`, { timeout: 5000 });
    return {
      status: 'success',
      data: response.data.data
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    };
  }
}

// Display server status
function displayServerStatus(serverStatus) {
  logSection('Server Status');
  
  if (serverStatus.status === 'online') {
    logSuccess(`Server: Online`);
    logInfo(`URL: ${BASE_URL}`);
    logInfo(`Response Time: ${serverStatus.responseTime}`);
    logInfo(`Timestamp: ${serverStatus.data.timestamp || 'N/A'}`);
  } else {
    logError(`Server: Offline`);
    logError(`Error: ${serverStatus.error}`);
    logWarning(`Please start the server with: npm run dev`);
  }
}

// Display database configuration
function displayDatabaseConfig(dbConfig) {
  logSection('Database Configuration');
  
  if (dbConfig.status === 'success') {
    const config = dbConfig.data;
    
    logInfo(`Provider: ${config.provider || 'auto-detect'}`);
    
    if (config.hasAivenUrl) {
      logSuccess(`AIVEN MySQL: Configured ✅`);
    } else {
      logWarning(`AIVEN MySQL: Not configured ⚠️`);
    }
    
    if (config.hasLocalUrl) {
      logSuccess(`Local MySQL: Configured ✅`);
    } else {
      logWarning(`Local MySQL: Not configured ⚠️`);
    }
    
    if (config.hasNeonUrl) {
      logSuccess(`NEON PostgreSQL: Configured ✅`);
    } else {
      logWarning(`NEON PostgreSQL: Not configured ⚠️`);
    }
    
    logSuccess(`SQLite: Always available ✅`);
    
    if (config.jwtSecret) {
      logSuccess(`JWT Secret: Configured ✅`);
    } else {
      logError(`JWT Secret: Missing ❌`);
    }
    
  } else {
    logError(`Configuration Error: ${dbConfig.error}`);
  }
}

// Display database status
function displayDatabaseStatus(dbStatus) {
  logSection('Database Status');
  
  if (dbStatus.status === 'success') {
    const status = dbStatus.data;
    
    logSuccess(`Current Provider: ${status.currentProvider || 'Unknown'}`);
    logInfo(`Connection Count: ${status.connectionCount || 'N/A'}`);
    logInfo(`Available Providers: ${status.availableProviders?.join(', ') || 'N/A'}`);
    logInfo(`Last Updated: ${status.timestamp || 'N/A'}`);
    
  } else {
    logWarning(`Database Status: Not available`);
    logInfo(`This is normal if the full server is not running`);
  }
}

// Display environment info
function displayEnvironmentInfo() {
  logSection('Environment Information');
  
  logInfo(`Node.js Version: ${process.version}`);
  logInfo(`Platform: ${process.platform}`);
  logInfo(`Architecture: ${process.arch}`);
  logInfo(`Working Directory: ${process.cwd()}`);
  logInfo(`Current Time: ${new Date().toLocaleString()}`);
}

// Display available commands
function displayCommands() {
  logSection('Available Commands');
  
  log(`📋 Testing Commands:`, 'cyan');
  logInfo(`  npm run test:db-quick      # Quick configuration + connection test`);
  logInfo(`  npm run test:db-config     # Configuration testing only`);
  logInfo(`  npm run test:db            # Connection testing`);
  logInfo(`  npm run test:db-all        # Complete test suite`);
  logInfo(`  npm run test:db-performance # Performance testing`);
  
  log(`\n🚀 Server Commands:`, 'cyan');
  logInfo(`  npm run dev                # Start development server`);
  logInfo(`  npm run build              # Build for production`);
  logInfo(`  npm run start              # Start production server`);
  
  log(`\n🔧 Manual Testing:`, 'cyan');
  logInfo(`  node test-endpoints.js     # Test basic endpoints`);
  logInfo(`  node test-server-simple.js # Simple server test`);
  logInfo(`  node database-status.js   # This status dashboard`);
}

// Main dashboard function
async function displayDashboard() {
  clearScreen();
  
  logHeader('SantriMental Database System - Status Dashboard');
  
  // Get all status information
  const serverStatus = await getServerStatus();
  const dbConfig = await getDatabaseConfig();
  const dbStatus = await getDatabaseStatus();
  
  // Display all sections
  displayServerStatus(serverStatus);
  displayDatabaseConfig(dbConfig);
  displayDatabaseStatus(dbStatus);
  displayEnvironmentInfo();
  displayCommands();
  
  // Overall system status
  logSection('Overall System Status');
  
  if (serverStatus.status === 'online' && dbConfig.status === 'success') {
    logSuccess('🎉 System Status: OPERATIONAL ✅');
    logSuccess('All core components are functioning correctly');
  } else if (serverStatus.status === 'online') {
    logWarning('⚠️  System Status: PARTIALLY OPERATIONAL');
    logWarning('Server is running but database configuration needs attention');
  } else {
    logError('❌ System Status: OFFLINE');
    logError('Server is not running - please start the server first');
  }
  
  log(`\n${'═'.repeat(80)}`, 'cyan');
  logInfo('Press Ctrl+C to exit, or run again to refresh status');
  log('═'.repeat(80), 'cyan');
}

// Run dashboard
displayDashboard().catch(error => {
  logError(`Dashboard error: ${error.message}`);
  process.exit(1);
});
