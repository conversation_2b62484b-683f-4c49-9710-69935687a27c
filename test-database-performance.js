#!/usr/bin/env node

/**
 * ⚡ Database Performance Testing Script
 * 
 * Tests database performance across all providers:
 * - Connection speed testing
 * - Query performance testing
 * - Concurrent operations testing
 * - Load testing
 * - Memory usage monitoring
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const BASE_URL = 'http://localhost:5000';
const API_BASE = `${BASE_URL}/api`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`⚡ ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(40));
  log(`📊 ${message}`, 'yellow');
  console.log('-'.repeat(40));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Performance test results
const performanceResults = {
  connectionSpeed: {},
  queryPerformance: {},
  concurrentOps: {},
  loadTesting: {},
  memoryUsage: {},
  summary: { total: 0, passed: 0, failed: 0, warnings: 0 }
};

function updateResult(category, test, status, details = '') {
  if (!performanceResults[category]) {
    performanceResults[category] = {};
  }
  
  performanceResults[category][test] = {
    status,
    details,
    timestamp: new Date().toISOString()
  };
  
  performanceResults.summary.total++;
  if (status === 'passed') performanceResults.summary.passed++;
  else if (status === 'failed') performanceResults.summary.failed++;
  else if (status === 'warning') performanceResults.summary.warnings++;
}

// Test connection speed for all providers
async function testConnectionSpeed() {
  logSubHeader('Connection Speed Testing');
  
  const providers = ['mysql', 'aiven-mysql', 'neon-postgresql', 'sqlite'];
  
  for (const provider of providers) {
    try {
      logInfo(`Testing connection speed for ${provider}...`);
      
      // Switch to provider
      const switchStart = Date.now();
      const switchResponse = await axios.post(`${API_BASE}/database/switch`, {
        provider: provider
      });
      const switchTime = Date.now() - switchStart;
      
      if (switchResponse.data.success) {
        // Test connection establishment
        const connectStart = Date.now();
        await axios.get(`${API_BASE}/database/status`);
        const connectTime = Date.now() - connectStart;
        
        const totalTime = switchTime + connectTime;
        
        if (totalTime < 1000) {
          logSuccess(`${provider}: ${totalTime}ms (Excellent) ✅`);
          updateResult('connectionSpeed', provider, 'passed', `${totalTime}ms`);
        } else if (totalTime < 3000) {
          logWarning(`${provider}: ${totalTime}ms (Good) ⚠️`);
          updateResult('connectionSpeed', provider, 'warning', `${totalTime}ms`);
        } else {
          logError(`${provider}: ${totalTime}ms (Slow) ❌`);
          updateResult('connectionSpeed', provider, 'failed', `${totalTime}ms`);
        }
        
        logInfo(`  Switch time: ${switchTime}ms, Connect time: ${connectTime}ms`);
        
      } else {
        logWarning(`${provider}: Switch failed - ${switchResponse.data.error} ⚠️`);
        updateResult('connectionSpeed', provider, 'warning', 'Switch failed');
      }
      
    } catch (error) {
      logError(`${provider}: Error - ${error.message} ❌`);
      updateResult('connectionSpeed', provider, 'failed', error.message);
    }
  }
}

// Test query performance
async function testQueryPerformance() {
  logSubHeader('Query Performance Testing');
  
  const queries = [
    { name: 'Simple Select', endpoint: '/database/status' },
    { name: 'Health Check', endpoint: '/database/health' },
    { name: 'Connection Test', endpoint: '/database/test-connections' }
  ];
  
  for (const query of queries) {
    try {
      logInfo(`Testing ${query.name}...`);
      
      // Run query multiple times to get average
      const iterations = 5;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        await axios.get(`${API_BASE}${query.endpoint}`);
        const time = Date.now() - start;
        times.push(time);
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      
      if (avgTime < 500) {
        logSuccess(`${query.name}: ${avgTime.toFixed(1)}ms avg (Fast) ✅`);
        updateResult('queryPerformance', query.name.replace(' ', '-'), 'passed', `${avgTime.toFixed(1)}ms avg`);
      } else if (avgTime < 1500) {
        logWarning(`${query.name}: ${avgTime.toFixed(1)}ms avg (Acceptable) ⚠️`);
        updateResult('queryPerformance', query.name.replace(' ', '-'), 'warning', `${avgTime.toFixed(1)}ms avg`);
      } else {
        logError(`${query.name}: ${avgTime.toFixed(1)}ms avg (Slow) ❌`);
        updateResult('queryPerformance', query.name.replace(' ', '-'), 'failed', `${avgTime.toFixed(1)}ms avg`);
      }
      
      logInfo(`  Min: ${minTime}ms, Max: ${maxTime}ms, Avg: ${avgTime.toFixed(1)}ms`);
      
    } catch (error) {
      logError(`${query.name}: Error - ${error.message} ❌`);
      updateResult('queryPerformance', query.name.replace(' ', '-'), 'failed', error.message);
    }
  }
}

// Test concurrent operations
async function testConcurrentOperations() {
  logSubHeader('Concurrent Operations Testing');
  
  const concurrencyLevels = [5, 10, 20];
  
  for (const level of concurrencyLevels) {
    try {
      logInfo(`Testing ${level} concurrent operations...`);
      
      const promises = [];
      const startTime = Date.now();
      
      // Create concurrent requests
      for (let i = 0; i < level; i++) {
        promises.push(axios.get(`${API_BASE}/database/status`));
      }
      
      // Wait for all to complete
      const results = await Promise.allSettled(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      const successRate = (successful / level) * 100;
      const avgTimePerOp = totalTime / level;
      
      if (successRate === 100 && avgTimePerOp < 200) {
        logSuccess(`${level} concurrent: ${successRate}% success, ${avgTimePerOp.toFixed(1)}ms/op ✅`);
        updateResult('concurrentOps', `level-${level}`, 'passed', `${successRate}% success, ${avgTimePerOp.toFixed(1)}ms/op`);
      } else if (successRate >= 90 && avgTimePerOp < 500) {
        logWarning(`${level} concurrent: ${successRate}% success, ${avgTimePerOp.toFixed(1)}ms/op ⚠️`);
        updateResult('concurrentOps', `level-${level}`, 'warning', `${successRate}% success, ${avgTimePerOp.toFixed(1)}ms/op`);
      } else {
        logError(`${level} concurrent: ${successRate}% success, ${avgTimePerOp.toFixed(1)}ms/op ❌`);
        updateResult('concurrentOps', `level-${level}`, 'failed', `${successRate}% success, ${avgTimePerOp.toFixed(1)}ms/op`);
      }
      
      logInfo(`  Successful: ${successful}, Failed: ${failed}, Total time: ${totalTime}ms`);
      
    } catch (error) {
      logError(`${level} concurrent operations: Error - ${error.message} ❌`);
      updateResult('concurrentOps', `level-${level}`, 'failed', error.message);
    }
  }
}

// Test load handling
async function testLoadHandling() {
  logSubHeader('Load Testing');
  
  try {
    logInfo('Running sustained load test...');
    
    const duration = 30000; // 30 seconds
    const requestInterval = 100; // Request every 100ms
    const startTime = Date.now();
    
    let requestCount = 0;
    let successCount = 0;
    let errorCount = 0;
    const responseTimes = [];
    
    const loadTest = async () => {
      while (Date.now() - startTime < duration) {
        try {
          const reqStart = Date.now();
          await axios.get(`${API_BASE}/database/status`);
          const reqTime = Date.now() - reqStart;
          
          responseTimes.push(reqTime);
          successCount++;
          
        } catch (error) {
          errorCount++;
        }
        
        requestCount++;
        await new Promise(resolve => setTimeout(resolve, requestInterval));
      }
    };
    
    await loadTest();
    
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const successRate = (successCount / requestCount) * 100;
    const requestsPerSecond = requestCount / (duration / 1000);
    
    if (successRate >= 95 && avgResponseTime < 1000) {
      logSuccess(`Load test: ${successRate.toFixed(1)}% success, ${avgResponseTime.toFixed(1)}ms avg, ${requestsPerSecond.toFixed(1)} req/s ✅`);
      updateResult('loadTesting', 'sustained-load', 'passed', `${successRate.toFixed(1)}% success, ${requestsPerSecond.toFixed(1)} req/s`);
    } else if (successRate >= 85 && avgResponseTime < 2000) {
      logWarning(`Load test: ${successRate.toFixed(1)}% success, ${avgResponseTime.toFixed(1)}ms avg, ${requestsPerSecond.toFixed(1)} req/s ⚠️`);
      updateResult('loadTesting', 'sustained-load', 'warning', `${successRate.toFixed(1)}% success, ${requestsPerSecond.toFixed(1)} req/s`);
    } else {
      logError(`Load test: ${successRate.toFixed(1)}% success, ${avgResponseTime.toFixed(1)}ms avg, ${requestsPerSecond.toFixed(1)} req/s ❌`);
      updateResult('loadTesting', 'sustained-load', 'failed', `${successRate.toFixed(1)}% success, ${requestsPerSecond.toFixed(1)} req/s`);
    }
    
    logInfo(`  Total requests: ${requestCount}, Success: ${successCount}, Errors: ${errorCount}`);
    logInfo(`  Duration: ${duration/1000}s, Requests/sec: ${requestsPerSecond.toFixed(1)}`);
    
  } catch (error) {
    logError(`Load testing failed: ${error.message}`);
    updateResult('loadTesting', 'sustained-load', 'failed', error.message);
  }
}

// Monitor memory usage (basic)
async function testMemoryUsage() {
  logSubHeader('Memory Usage Monitoring');
  
  try {
    const initialMemory = process.memoryUsage();
    logInfo(`Initial memory usage: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    
    // Perform some operations
    logInfo('Performing memory-intensive operations...');
    
    for (let i = 0; i < 100; i++) {
      await axios.get(`${API_BASE}/database/status`);
    }
    
    const finalMemory = process.memoryUsage();
    const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024;
    
    logInfo(`Final memory usage: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    logInfo(`Memory increase: ${memoryIncrease.toFixed(2)} MB`);
    
    if (memoryIncrease < 10) {
      logSuccess(`Memory usage: ${memoryIncrease.toFixed(2)} MB increase (Good) ✅`);
      updateResult('memoryUsage', 'operations', 'passed', `${memoryIncrease.toFixed(2)} MB increase`);
    } else if (memoryIncrease < 50) {
      logWarning(`Memory usage: ${memoryIncrease.toFixed(2)} MB increase (Acceptable) ⚠️`);
      updateResult('memoryUsage', 'operations', 'warning', `${memoryIncrease.toFixed(2)} MB increase`);
    } else {
      logError(`Memory usage: ${memoryIncrease.toFixed(2)} MB increase (High) ❌`);
      updateResult('memoryUsage', 'operations', 'failed', `${memoryIncrease.toFixed(2)} MB increase`);
    }
    
  } catch (error) {
    logError(`Memory usage testing failed: ${error.message}`);
    updateResult('memoryUsage', 'operations', 'failed', error.message);
  }
}

// Generate performance report
function generatePerformanceReport() {
  logHeader('Performance Test Report');
  
  const { total, passed, failed, warnings } = performanceResults.summary;
  
  logInfo(`Total Tests: ${total}`);
  logSuccess(`Passed: ${passed}`);
  logError(`Failed: ${failed}`);
  logWarning(`Warnings: ${warnings}`);
  
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
  logInfo(`Success Rate: ${successRate}%`);
  
  // Performance recommendations
  logSubHeader('Performance Recommendations');
  
  if (failed > 0) {
    logError('❌ Performance issues detected. Consider:');
    logError('   - Optimizing database queries');
    logError('   - Adding database indexes');
    logError('   - Upgrading database resources');
    logError('   - Implementing connection pooling');
  } else if (warnings > 0) {
    logWarning('⚠️  Some performance metrics could be improved:');
    logWarning('   - Monitor database performance regularly');
    logWarning('   - Consider caching for frequently accessed data');
    logWarning('   - Optimize slow queries');
  } else {
    logSuccess('🎉 Excellent performance across all metrics!');
  }
  
  // Save detailed report
  const reportPath = path.join(__dirname, 'database-performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(performanceResults, null, 2));
  logInfo(`Detailed report saved to: ${reportPath}`);
}

// Main performance test runner
async function runPerformanceTests() {
  logHeader('Database Performance Testing Suite');
  
  await testConnectionSpeed();
  await testQueryPerformance();
  await testConcurrentOperations();
  await testLoadHandling();
  await testMemoryUsage();
  generatePerformanceReport();
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPerformanceTests().catch(error => {
    logError(`Performance test suite failed: ${error.message}`);
    process.exit(1);
  });
}

export {
  runPerformanceTests,
  performanceResults
};
