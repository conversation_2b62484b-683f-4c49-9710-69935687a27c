import nodemailer from 'nodemailer';

// Email configuration
const EMAIL_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER || '<EMAIL>',
    pass: process.env.SMTP_PASS || 'your-app-password'
  }
};

// Create transporter
const transporter = nodemailer.createTransport(EMAIL_CONFIG);

// Verify connection configuration (only if credentials are provided)
if (EMAIL_CONFIG.auth.user !== '<EMAIL>' && EMAIL_CONFIG.auth.pass !== 'your-app-password') {
  transporter.verify(function(error, success) {
    if (error) {
      console.log('❌ Email service error:', error);
    } else {
      console.log('✅ Email service ready');
    }
  });
} else {
  console.log('⚠️ Email service disabled - Update SMTP credentials in .env to enable');
}

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export async function sendEmail(options: EmailOptions): Promise<boolean> {
  // Skip email if credentials not configured
  if (EMAIL_CONFIG.auth.user === '<EMAIL>' || EMAIL_CONFIG.auth.pass === 'your-app-password') {
    console.log('⚠️ Email skipped - SMTP credentials not configured');
    console.log(`📧 Would send email to: ${options.to}`);
    console.log(`📧 Subject: ${options.subject}`);
    return true; // Return true to not break the flow
  }

  try {
    const mailOptions = {
      from: `"SantriMental" <${EMAIL_CONFIG.auth.user}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || options.html.replace(/<[^>]*>/g, '') // Strip HTML for text version
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('❌ Email send error:', error);
    return false;
  }
}

export function generateWelcomeEmail(email: string): string {
  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Selamat Datang di SantriMental</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8f9fa;
        }
        .container {
          background: white;
          border-radius: 10px;
          padding: 30px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #10b981, #059669);
          border-radius: 50%;
          margin: 0 auto 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          font-weight: bold;
        }
        .title {
          color: #10b981;
          font-size: 24px;
          font-weight: bold;
          margin: 0;
        }
        .subtitle {
          color: #6b7280;
          margin: 5px 0 0 0;
        }
        .content {
          margin: 20px 0;
        }
        .highlight {
          background: #f0fdf4;
          border-left: 4px solid #10b981;
          padding: 15px;
          margin: 20px 0;
          border-radius: 0 5px 5px 0;
        }
        .button {
          display: inline-block;
          background: linear-gradient(135deg, #10b981, #059669);
          color: white;
          padding: 12px 30px;
          text-decoration: none;
          border-radius: 25px;
          font-weight: bold;
          margin: 20px 0;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          color: #6b7280;
          font-size: 14px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">SM</div>
          <h1 class="title">Selamat Datang di SantriMental!</h1>
          <p class="subtitle">Platform Kesehatan Mental untuk Santri Pesantren</p>
        </div>
        
        <div class="content">
          <p>Assalamu'alaikum dan selamat datang, <strong>${email}</strong>!</p>
          
          <p>Terima kasih telah bergabung dengan SantriMental. Kami sangat senang Anda menjadi bagian dari komunitas yang peduli dengan kesehatan mental santri.</p>
          
          <div class="highlight">
            <strong>Apa yang bisa Anda lakukan di SantriMental:</strong>
            <ul>
              <li>🧠 Mengikuti assessment kesehatan mental yang tervalidasi</li>
              <li>📚 Mengakses materi edukasi kesehatan mental Islami</li>
              <li>💬 Mendapatkan panduan dan saran yang sesuai syariat</li>
              <li>📊 Memantau perkembangan kesehatan mental Anda</li>
            </ul>
          </div>
          
          <p>Akun Anda telah aktif dan siap digunakan. Silakan login untuk mulai menjelajahi fitur-fitur yang tersedia.</p>
          
          <div style="text-align: center;">
            <a href="${process.env.FRONTEND_URL || 'http://localhost:5000'}/login" class="button">
              Mulai Sekarang
            </a>
          </div>
        </div>
        
        <div class="footer">
          <p>Email ini dikirim otomatis dari sistem SantriMental.</p>
          <p>Jika Anda memiliki pertanyaan, silakan hubungi tim support kami.</p>
          <p><strong>Barakallahu fiikum</strong></p>
        </div>
      </div>
    </body>
    </html>
  `;
}

export async function sendWelcomeEmail(email: string): Promise<boolean> {
  const emailOptions: EmailOptions = {
    to: email,
    subject: '🌟 Selamat Datang di SantriMental - Akun Anda Telah Aktif!',
    html: generateWelcomeEmail(email)
  };

  return await sendEmail(emailOptions);
}
