import { ConnectionManager } from './database/connection-manager';
import { getDatabaseConfig } from './config/database';

// Initialize connection manager
const connectionManager = ConnectionManager.getInstance();

// Export database connection
export async function getDb() {
  const connection = await connectionManager.getConnection();
  return connection.db;
}

// Export pool for raw queries (MySQL only)
export async function getPool() {
  const connection = await connectionManager.getConnection();
  return connection.pool || connection.getPool?.();
}

// Export current provider info
export function getCurrentProvider() {
  return connectionManager.getCurrentProvider();
}

// Health check function
export async function healthCheck() {
  try {
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();
    
    if (connection.testConnection) {
      const isHealthy = await connection.testConnection();
      return { provider, healthy: isHealthy };
    }
    
    return { provider, healthy: true };
  } catch (error) {
    return { provider: 'unknown', healthy: false, error: error.message };
  }
}