# SantriMental - Mental Health Assessment Platform

## Project Overview
SantriMental is a comprehensive mental health assessment platform designed for Islamic boarding school (pesantren) students. The application provides psychological screening tools, therapy resources, and educational content focused on mental health awareness in Islamic educational contexts.

## Architecture
- **Frontend**: React with TypeScript, Vite, TailwindCSS, Shadcn/UI components
- **Backend**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT-based with bcrypt password hashing
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query for server state

## Key Features
1. **Assessment Tools**: 
   - DASS-42 (Depression, Anxiety, Stress Scale)
   - GSE (General Self-Efficacy Scale)
   - MHKQ (Mental Health Knowledge Questionnaire)
   - MSPSS (Multidimensional Scale of Perceived Social Support)
   - PDD (Perceived Devaluation-Discrimination Scale)

2. **User Management**: Registration, authentication, and profile management
3. **Educational Content**: Interactive modules, videos, and embedded games including bullying prevention game (GEN ZAS)
4. **Data Persistence**: Local storage with cloud synchronization
5. **Responsive Design**: Mobile-first approach optimized for various devices

## Recent Changes (Migration from Lovable to Replit)
- **Date**: Current session
- **Changes**:
  - Migrated from Supabase to PostgreSQL with Drizzle ORM
  - Replaced Supabase authentication with custom JWT-based auth system
  - Converted from React Router to Wouter routing
  - Created secure API endpoints for all data operations
  - Implemented proper client/server separation for security
  - Set up database schema with users, profiles, and assessments tables
  - Removed all external dependencies on Supabase services
  - Added embedded bullying prevention game (GEN ZAS by Mizu Izumi) to Education section
  - Enhanced responsive design for all modals and media content
  - Implemented HTML5 video player for Google Drive videos
  - Optimized PDF viewing for mobile devices
  - Improved iframe embedding for games with proper aspect ratios
  - Added comprehensive "Learn More" information modals for all assessments
  - Generated and integrated relevant images for each assessment and feature
  - Enhanced Features section with detailed information dialogs
  - Created educational content explaining each assessment tool and platform feature
  - Improved visual design with hero images and assessment-specific illustrations

## Database Schema
- **users**: Authentication and basic user information
- **profiles**: Extended user profile data (name, institution, demographics)
- **assessments**: Assessment results and metadata

## API Endpoints
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User authentication
- `GET /api/auth/user` - Get current user
- `POST /api/assessments` - Create assessment result
- `GET /api/assessments` - Get user's assessments
- `GET|POST|PUT /api/profile` - Profile management

## User Preferences
- Language: Indonesian (Bahasa Indonesia)
- Target audience: Non-technical pesantren administrators and students
- Security priority: High - proper authentication and data protection
- Design approach: Mobile-first, accessible, culturally appropriate

## Development Notes
- Uses PostgreSQL database provided by Replit environment
- Environment variables managed securely through Replit secrets
- Development mode uses in-memory storage, production uses database
- JWT secret should be configured for production deployment

## Deployment Status
- Successfully migrated to Replit environment
- All dependencies installed and configured
- Database schema deployed
- Authentication system functional
- Ready for user testing and feedback