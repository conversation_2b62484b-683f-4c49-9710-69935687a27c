#!/usr/bin/env node

/**
 * 🔍 Test JWT Configuration
 * 
 * Tests JWT token generation and validation
 */

import 'dotenv/config';
import jwt from 'jsonwebtoken';

console.log('🔍 JWT Configuration Test');
console.log('='.repeat(40));

// Check JWT_SECRET
const jwtSecret = process.env.JWT_SECRET;
console.log(`JWT_SECRET exists: ${jwtSecret ? '✅ YES' : '❌ NO'}`);
if (jwtSecret) {
  console.log(`JWT_SECRET length: ${jwtSecret.length} characters`);
  console.log(`JWT_SECRET preview: ${jwtSecret.substring(0, 10)}...`);
}

if (!jwtSecret) {
  console.log('\n❌ JWT_SECRET is not configured!');
  console.log('Please add JWT_SECRET to your .env file');
  process.exit(1);
}

// Test token generation
console.log('\n🔧 Testing token generation...');
try {
  const testPayload = {
    userId: 'test-123',
    email: '<EMAIL>',
    role: 'admin'
  };
  
  const token = jwt.sign(testPayload, jwtSecret, { expiresIn: '24h' });
  console.log('✅ Token generated successfully');
  console.log(`Token: ${token.substring(0, 50)}...`);
  
  // Test token verification
  console.log('\n🔍 Testing token verification...');
  const decoded = jwt.verify(token, jwtSecret);
  console.log('✅ Token verified successfully');
  console.log('Decoded payload:', decoded);
  
} catch (error) {
  console.log('❌ JWT test failed:', error.message);
}

console.log('\n' + '='.repeat(40));
