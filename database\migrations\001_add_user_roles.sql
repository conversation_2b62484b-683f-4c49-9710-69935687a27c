-- Migration: Add role-based access control to users table
-- Version: 1.0.0
-- Date: 2025-08-14

-- Add role column to users table
ALTER TABLE users 
ADD COLUMN role VARCHAR(20) DEFAULT 'user' NOT NULL AFTER password;

-- Add additional user management fields
ALTER TABLE users 
ADD COLUMN is_active BOOLEAN DEFAULT TRUE NOT NULL AFTER role,
ADD COLUMN last_login_at TIMESTAMP NULL AFTER is_active,
ADD COLUMN email_verified BOOLEAN DEFAULT FALSE NOT NULL AFTER last_login_at,
ADD COLUMN email_verified_at TIMESTAMP NULL AFTER email_verified;

-- Create index for role-based queries
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_email_verified ON users(email_verified);

-- Create admin user if not exists
INSERT IGNORE INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
VALUES (
  UUID(),
  '<EMAIL>',
  '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
  'admin',
  TRUE,
  TRUE,
  NOW(),
  NOW()
);

-- Update existing users to have default role
UPDATE users SET role = 'user' WHERE role IS NULL OR role = '';

-- Create user_sessions table for session management
CREATE TABLE IF NOT EXISTS user_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  session_token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_sessions_token (session_token),
  INDEX idx_sessions_user (user_id),
  INDEX idx_sessions_expires (expires_at)
);

-- Create user_permissions table for granular permissions
CREATE TABLE IF NOT EXISTS user_permissions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  permission VARCHAR(100) NOT NULL,
  granted_by VARCHAR(36),
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE KEY unique_user_permission (user_id, permission),
  INDEX idx_permissions_user (user_id),
  INDEX idx_permissions_permission (permission)
);

-- Insert default admin permissions
INSERT IGNORE INTO user_permissions (id, user_id, permission, granted_at)
SELECT 
  UUID(),
  u.id,
  p.permission,
  NOW()
FROM users u
CROSS JOIN (
  SELECT 'admin.dashboard' as permission
  UNION SELECT 'admin.users.view'
  UNION SELECT 'admin.users.create'
  UNION SELECT 'admin.users.edit'
  UNION SELECT 'admin.users.delete'
  UNION SELECT 'admin.assessments.view'
  UNION SELECT 'admin.assessments.manage'
  UNION SELECT 'admin.reports.view'
  UNION SELECT 'admin.reports.export'
  UNION SELECT 'admin.system.settings'
  UNION SELECT 'admin.system.logs'
  UNION SELECT 'admin.database.manage'
) p
WHERE u.role = 'admin';

-- Create audit_logs table for tracking admin actions
CREATE TABLE IF NOT EXISTS audit_logs (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50),
  resource_id VARCHAR(36),
  old_values JSON,
  new_values JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_audit_user (user_id),
  INDEX idx_audit_action (action),
  INDEX idx_audit_resource (resource_type, resource_id),
  INDEX idx_audit_created (created_at)
);
