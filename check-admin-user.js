#!/usr/bin/env node

/**
 * 🔍 Check Admin User
 * 
 * Checks admin user in database and creates if needed
 */

import 'dotenv/config';
import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔍 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function checkAdminUser() {
  logHeader('Admin User Check & Setup');
  
  try {
    const url = process.env.AIVEN_MYSQL_URL || process.env.DATABASE_URL;
    if (!url) {
      throw new Error('No database URL found');
    }
    
    logInfo('Connecting to database...');
    const connection = await mysql.createConnection(url);
    
    // Check if admin user exists
    const [adminUsers] = await connection.execute(
      "SELECT id, username, email, password_hash, role FROM users WHERE email = ? OR role = 'admin'",
      ['<EMAIL>']
    );
    
    if (adminUsers.length > 0) {
      logInfo('Found existing admin users:');
      adminUsers.forEach(user => {
        console.log(`  - ${user.email} (${user.role}) - ID: ${user.id}`);
      });
      
      // Test password hash
      const adminUser = adminUsers.find(u => u.email === '<EMAIL>');
      if (adminUser) {
        logInfo('Testing password hash...');
        const isValid = await bcrypt.compare('admin123', adminUser.password_hash);
        if (isValid) {
          logSuccess('Password hash is valid');
        } else {
          logError('Password hash is invalid - need to update');
          
          // Update password hash
          const newHash = await bcrypt.hash('admin123', 10);
          await connection.execute(
            'UPDATE users SET password_hash = ? WHERE id = ?',
            [newHash, adminUser.id]
          );
          logSuccess('Password hash updated');
        }
      }
    } else {
      logInfo('No admin user found, creating one...');
      
      // Create admin user
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      await connection.execute(`
        INSERT INTO users (username, email, password_hash, first_name, role, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, 'admin', 'active', NOW(), NOW())
      `, ['admin', '<EMAIL>', hashedPassword, 'Administrator']);
      
      logSuccess('Admin user created successfully');
    }
    
    // Show final admin user
    const [finalAdmin] = await connection.execute(
      "SELECT id, username, email, first_name, role, status FROM users WHERE email = ?",
      ['<EMAIL>']
    );
    
    if (finalAdmin.length > 0) {
      const admin = finalAdmin[0];
      logSuccess('Admin user ready:');
      logInfo(`  Email: ${admin.email}`);
      logInfo(`  Username: ${admin.username}`);
      logInfo(`  Name: ${admin.first_name}`);
      logInfo(`  Role: ${admin.role}`);
      logInfo(`  Status: ${admin.status}`);
      logInfo(`  Password: admin123`);
    }
    
    await connection.end();
    logSuccess('Admin user check completed');
    
  } catch (error) {
    logError(`Check failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run check
checkAdminUser();
