import { useEffect, useState } from "react";
import { apiRequest } from "@/lib/queryClient";

interface User {
  id: string;
  email: string;
}

interface Session {
  user: User;
  access_token: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  const signIn = async (email: string, password: string) => {
    try {
      const response = await apiRequest('/api/auth/signin', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });
      
      localStorage.setItem('auth_token', response.access_token);
      const newSession = { user: response.user, access_token: response.access_token };
      setSession(newSession);
      setUser(response.user);
      
      return { data: { user: response.user, session: newSession }, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      const response = await apiRequest('/api/auth/signup', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });
      
      localStorage.setItem('auth_token', response.access_token);
      const newSession = { user: response.user, access_token: response.access_token };
      setSession(newSession);
      setUser(response.user);
      
      return { data: { user: response.user, session: newSession }, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  };

  const signOut = async () => {
    localStorage.removeItem('auth_token');
    setSession(null);
    setUser(null);
  };

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      // Validate token by fetching user
      apiRequest('/api/auth/user')
        .then((userData) => {
          setUser(userData);
          setSession({ user: userData, access_token: token });
        })
        .catch(() => {
          // Token is invalid, clear it
          localStorage.removeItem('auth_token');
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, []);

  return { user, session, loading, signIn, signUp, signOut };
}
