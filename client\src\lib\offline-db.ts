import Dexie, { Table } from 'dexie';

export interface OfflineUser {
  id: string;
  email: string;
  password: string;
  createdAt: Date;
  updatedAt: Date;
  synced: boolean;
  lastSync?: Date;
}

export interface OfflineProfile {
  id: string;
  userId: string;
  namaLengkap: string;
  nomorInduk?: string;
  jenisKelamin?: string;
  tanggalLahir?: string;
  kelas?: string;
  pondokPesantren?: string;
  createdAt: Date;
  updatedAt: Date;
  synced: boolean;
  lastSync?: Date;
}

export interface OfflineAssessmentConfig {
  id: string;
  assessmentCode: string;
  assessmentName: string;
  description?: string;
  version: string;
  totalItems: number;
  estimatedTimeMinutes: number;
  isActive: boolean;
  requiresSupervision: boolean;
  ageMin: number;
  ageMax: number;
  culturalContext: string;
  scoringMethod: string;
  clinicalCutoffs?: string;
  psychometricProperties?: string;
  createdAt: Date;
  updatedAt: Date;
  synced: boolean;
  lastSync?: Date;
}

export interface OfflineAssessmentSession {
  id: string;
  userId: string;
  assessmentCode: string;
  sessionStatus: string;
  startedAt: Date;
  completedAt?: Date;
  durationSeconds?: number;
  ipAddress?: string;
  userAgent?: string;
  deviceType?: string;
  isSupervised: boolean;
  supervisorId?: string;
  notes?: string;
  synced: boolean;
  lastSync?: Date;
}

export interface OfflineAssessmentResult {
  id: string;
  sessionId: string;
  assessmentCode: string;
  userId: string;
  totalRawScore?: string;
  domainScores?: string;
  totalTScore?: string;
  totalPercentile?: string;
  domainTScores?: string;
  domainPercentiles?: string;
  overallSeverity?: string;
  domainSeverities?: string;
  riskLevel: string;
  interpretationSummary?: string;
  clinicalRecommendations?: string;
  referralRecommended: boolean;
  followUpRecommended: boolean;
  followUpTimeframe?: string;
  reliabilityAlpha?: string;
  responseConsistency?: string;
  completionPercentage: string;
  validityFlags?: string;
  religiousCopingIndicators?: string;
  culturalConsiderations?: string;
  createdAt: Date;
  updatedAt: Date;
  synced: boolean;
  lastSync?: Date;
}

export interface SyncQueueItem {
  id: string;
  tableName: string;
  recordId: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  data: any;
  createdAt: Date;
  retryCount: number;
  lastError?: string;
}

export class OfflineDatabase extends Dexie {
  users!: Table<OfflineUser>;
  profiles!: Table<OfflineProfile>;
  assessmentConfigs!: Table<OfflineAssessmentConfig>;
  assessmentSessions!: Table<OfflineAssessmentSession>;
  assessmentResults!: Table<OfflineAssessmentResult>;
  syncQueue!: Table<SyncQueueItem>;

  constructor() {
    super('SantriMentalDB');
    
    this.version(1).stores({
      users: 'id, email, synced, lastSync',
      profiles: 'id, userId, synced, lastSync',
      assessmentConfigs: 'id, assessmentCode, isActive, synced, lastSync',
      assessmentSessions: 'id, userId, assessmentCode, sessionStatus, synced, lastSync',
      assessmentResults: 'id, sessionId, userId, assessmentCode, synced, lastSync',
      syncQueue: 'id, tableName, operation, createdAt'
    });
  }
}

export const offlineDb = new OfflineDatabase();

// Offline storage service
export class OfflineStorageService {
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;

  constructor() {
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.syncToServer();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Start periodic sync
    this.startPeriodicSync();
  }

  private startPeriodicSync() {
    setInterval(async () => {
      if (this.isOnline && !this.syncInProgress) {
        await this.syncToServer();
      }
    }, 30000); // Sync every 30 seconds
  }

  // Add to sync queue
  private async addToSyncQueue(tableName: string, recordId: string, operation: 'INSERT' | 'UPDATE' | 'DELETE', data: any) {
    await offlineDb.syncQueue.add({
      id: crypto.randomUUID(),
      tableName,
      recordId,
      operation,
      data,
      createdAt: new Date(),
      retryCount: 0
    });
  }

  // User operations
  async createUser(user: Omit<OfflineUser, 'id' | 'createdAt' | 'updatedAt' | 'synced'>): Promise<OfflineUser> {
    const newUser: OfflineUser = {
      ...user,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
      synced: false
    };

    await offlineDb.users.add(newUser);
    await this.addToSyncQueue('users', newUser.id, 'INSERT', newUser);

    return newUser;
  }

  async getUserByEmail(email: string): Promise<OfflineUser | undefined> {
    return await offlineDb.users.where('email').equals(email).first();
  }

  async getUser(id: string): Promise<OfflineUser | undefined> {
    return await offlineDb.users.get(id);
  }

  // Profile operations
  async createProfile(profile: Omit<OfflineProfile, 'id' | 'createdAt' | 'updatedAt' | 'synced'>): Promise<OfflineProfile> {
    const newProfile: OfflineProfile = {
      ...profile,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
      synced: false
    };

    await offlineDb.profiles.add(newProfile);
    await this.addToSyncQueue('profiles', newProfile.id, 'INSERT', newProfile);

    return newProfile;
  }

  async getProfile(userId: string): Promise<OfflineProfile | undefined> {
    return await offlineDb.profiles.where('userId').equals(userId).first();
  }

  // Assessment operations
  async createAssessmentSession(session: Omit<OfflineAssessmentSession, 'id' | 'synced'>): Promise<OfflineAssessmentSession> {
    const newSession: OfflineAssessmentSession = {
      ...session,
      id: crypto.randomUUID(),
      synced: false
    };

    await offlineDb.assessmentSessions.add(newSession);
    await this.addToSyncQueue('assessmentSessions', newSession.id, 'INSERT', newSession);

    return newSession;
  }

  async createAssessmentResult(result: Omit<OfflineAssessmentResult, 'id' | 'createdAt' | 'updatedAt' | 'synced'>): Promise<OfflineAssessmentResult> {
    const newResult: OfflineAssessmentResult = {
      ...result,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date(),
      synced: false
    };

    await offlineDb.assessmentResults.add(newResult);
    await this.addToSyncQueue('assessmentResults', newResult.id, 'INSERT', newResult);

    return newResult;
  }

  async getAssessmentResultsByUser(userId: string): Promise<OfflineAssessmentResult[]> {
    return await offlineDb.assessmentResults.where('userId').equals(userId).toArray();
  }

  async getAssessmentConfigs(): Promise<OfflineAssessmentConfig[]> {
    return await offlineDb.assessmentConfigs.where('isActive').equals(true).toArray();
  }

  // Sync operations
  async syncToServer(): Promise<void> {
    if (!this.isOnline || this.syncInProgress) return;

    this.syncInProgress = true;
    console.log('🔄 Starting client sync...');

    try {
      const pendingItems = await offlineDb.syncQueue.orderBy('createdAt').limit(50).toArray();

      for (const item of pendingItems) {
        try {
          await this.syncItem(item);
          await offlineDb.syncQueue.delete(item.id);
        } catch (error) {
          await offlineDb.syncQueue.update(item.id, {
            retryCount: item.retryCount + 1,
            lastError: error.message
          });
          console.error(`❌ Client sync failed for ${item.tableName}:${item.recordId}:`, error.message);
        }
      }

      console.log(`✅ Client sync completed. Processed ${pendingItems.length} items.`);
    } catch (error) {
      console.error('❌ Client sync process failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncItem(item: SyncQueueItem): Promise<void> {
    const endpoint = this.getEndpointForTable(item.tableName);
    
    if (item.operation === 'INSERT') {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(item.data)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Mark as synced in local database
      await this.markAsSynced(item.tableName, item.recordId);
    }
  }

  private getEndpointForTable(tableName: string): string {
    const baseUrl = '/api';
    switch (tableName) {
      case 'users': return `${baseUrl}/auth/signup`;
      case 'profiles': return `${baseUrl}/profile`;
      case 'assessmentSessions': return `${baseUrl}/assessment-sessions`;
      case 'assessmentResults': return `${baseUrl}/assessment-results`;
      default: throw new Error(`Unknown table: ${tableName}`);
    }
  }

  private async markAsSynced(tableName: string, recordId: string): Promise<void> {
    const now = new Date();
    switch (tableName) {
      case 'users':
        await offlineDb.users.update(recordId, { synced: true, lastSync: now });
        break;
      case 'profiles':
        await offlineDb.profiles.update(recordId, { synced: true, lastSync: now });
        break;
      case 'assessmentSessions':
        await offlineDb.assessmentSessions.update(recordId, { synced: true, lastSync: now });
        break;
      case 'assessmentResults':
        await offlineDb.assessmentResults.update(recordId, { synced: true, lastSync: now });
        break;
    }
  }

  // Get sync status
  async getSyncStatus(): Promise<{ pending: number; lastSync: Date | null; isOnline: boolean }> {
    const pending = await offlineDb.syncQueue.count();
    
    // Get latest sync time across all tables
    const [userSync, profileSync, sessionSync, resultSync] = await Promise.all([
      offlineDb.users.orderBy('lastSync').reverse().first(),
      offlineDb.profiles.orderBy('lastSync').reverse().first(),
      offlineDb.assessmentSessions.orderBy('lastSync').reverse().first(),
      offlineDb.assessmentResults.orderBy('lastSync').reverse().first()
    ]);

    const lastSyncTimes = [userSync?.lastSync, profileSync?.lastSync, sessionSync?.lastSync, resultSync?.lastSync]
      .filter(Boolean) as Date[];

    const lastSync = lastSyncTimes.length > 0 ? new Date(Math.max(...lastSyncTimes.map(d => d.getTime()))) : null;

    return {
      pending,
      lastSync,
      isOnline: this.isOnline
    };
  }
}

export const offlineStorage = new OfflineStorageService();
