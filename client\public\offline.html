<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - SantriMental</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }

        h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 24px;
            opacity: 0.9;
        }

        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: left;
        }

        .features h3 {
            font-size: 18px;
            margin-bottom: 12px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 12px;
            background: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .retry-btn {
            background: white;
            color: #059669;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
            margin-bottom: 16px;
        }

        .retry-btn:hover {
            background: #f3f4f6;
            transform: translateY(-1px);
        }

        .status {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 16px;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 16px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .icon {
                width: 64px;
                height: 64px;
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            📱
        </div>
        
        <h1>Mode Offline Aktif</h1>
        
        <p>Anda sedang offline, tetapi SantriMental tetap dapat digunakan dengan fitur terbatas.</p>
        
        <div class="features">
            <h3>Fitur yang Tersedia Offline:</h3>
            <ul class="feature-list">
                <li>Mengisi assessment kesehatan mental</li>
                <li>Melihat riwayat assessment tersimpan</li>
                <li>Membaca materi edukasi yang sudah diunduh</li>
                <li>Data akan disinkronkan saat online</li>
            </ul>
        </div>
        
        <button class="retry-btn" onclick="checkConnection()">
            <span id="retry-text">Coba Sambung Lagi</span>
        </button>
        
        <div class="status">
            <div id="connection-status">Memeriksa koneksi...</div>
            <div id="last-sync"></div>
        </div>
    </div>

    <script>
        let isChecking = false;

        function checkConnection() {
            if (isChecking) return;
            
            isChecking = true;
            const retryBtn = document.getElementById('retry-text');
            const status = document.getElementById('connection-status');
            
            retryBtn.textContent = 'Memeriksa...';
            status.textContent = 'Mencoba menyambung ke server...';
            
            fetch('/', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok) {
                    status.textContent = 'Koneksi berhasil! Mengalihkan...';
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    throw new Error('Server tidak merespons');
                }
            })
            .catch(error => {
                status.textContent = 'Masih offline. Coba lagi nanti.';
                retryBtn.textContent = 'Coba Sambung Lagi';
                isChecking = false;
            });
        }

        function updateConnectionStatus() {
            const status = document.getElementById('connection-status');
            const lastSync = document.getElementById('last-sync');
            
            if (navigator.onLine) {
                status.textContent = 'Koneksi terdeteksi, memeriksa server...';
                checkConnection();
            } else {
                status.textContent = 'Tidak ada koneksi internet';
            }

            // Show last sync time if available
            const lastSyncTime = localStorage.getItem('lastSyncTime');
            if (lastSyncTime) {
                const date = new Date(lastSyncTime);
                lastSync.textContent = `Sinkronisasi terakhir: ${date.toLocaleString('id-ID')}`;
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Check connection status on load
        updateConnectionStatus();

        // Auto-retry every 30 seconds
        setInterval(() => {
            if (navigator.onLine && !isChecking) {
                checkConnection();
            }
        }, 30000);

        // Listen for service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'SYNC_COMPLETE') {
                    localStorage.setItem('lastSyncTime', new Date().toISOString());
                    updateConnectionStatus();
                }
            });
        }
    </script>
</body>
</html>
