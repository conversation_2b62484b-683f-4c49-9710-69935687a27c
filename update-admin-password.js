#!/usr/bin/env node

/**
 * 🔧 Update Admin Password
 * 
 * Updates admin password with proper bcrypt hash
 */

import 'dotenv/config';
import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';

async function updateAdminPassword() {
  try {
    const url = process.env.AIVEN_MYSQL_URL || process.env.DATABASE_URL;
    const connection = await mysql.createConnection(url);
    
    console.log('🔧 Updating admin password...');
    
    // Generate new hash for 'admin123'
    const newPassword = 'admin123';
    const newHash = await bcrypt.hash(newPassword, 10);
    
    console.log(`New password: ${newPassword}`);
    console.log(`New hash: ${newHash}`);
    
    // Update admin password
    const [result] = await connection.execute(
      'UPDATE users SET password_hash = ? WHERE email = ?',
      [newHash, '<EMAIL>']
    );
    
    if (result.affectedRows > 0) {
      console.log('✅ Admin password updated successfully');
      
      // Verify the update
      const [adminUsers] = await connection.execute(
        "SELECT id, email, password_hash FROM users WHERE email = ?",
        ['<EMAIL>']
      );
      
      if (adminUsers.length > 0) {
        const admin = adminUsers[0];
        const isValid = await bcrypt.compare(newPassword, admin.password_hash);
        console.log(`Password verification: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
      }
      
    } else {
      console.log('❌ No admin user found to update');
    }
    
    await connection.end();
    
    console.log('\n🎉 Admin credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

updateAdminPassword();
