import { Router } from 'express';
import { healthCheck, getCurrentProvider } from '../db-flexible';
import { ConnectionManager } from '../database/connection-manager';
import { DatabaseMigrator } from '../database/migrator';

const router = Router();

// Database health check endpoint
router.get('/health', async (req, res) => {
  try {
    const health = await healthCheck();
    res.json({
      status: health.healthy ? 'healthy' : 'unhealthy',
      provider: health.provider,
      timestamp: new Date().toISOString(),
      error: health.error || null
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Switch database provider endpoint
router.post('/switch-provider', async (req, res) => {
  try {
    const { provider } = req.body;
    const connectionManager = ConnectionManager.getInstance();
    
    await connectionManager.switchProvider(provider);
    
    res.json({
      success: true,
      message: `Switched to ${provider}`,
      currentProvider: connectionManager.getCurrentProvider()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Migration endpoint
router.post('/migrate-to-aiven', async (req, res) => {
  try {
    const migrator = new DatabaseMigrator();
    await migrator.migrateToAiven();
    
    res.json({
      success: true,
      message: 'Migration to AIVEN completed successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

export default router;