import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { RefreshCw, Database, Activity, AlertTriangle, CheckCircle, Clock, Server } from 'lucide-react';
import Navbar from '@/components/sections/Navbar';
import Footer from '@/components/sections/Footer';

interface DatabaseStatus {
  currentProvider: string;
  availableProviders: string[];
  connectionCount: number;
  timestamp: string;
  health: {
    overall: 'healthy' | 'degraded' | 'unhealthy';
    checks: Array<{
      provider: string;
      status: 'healthy' | 'unhealthy';
      responseTime: number;
      timestamp: string;
      error?: string;
    }>;
    uptime: number;
  };
}

export default function DatabaseDashboard() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [switching, setSwitching] = useState(false);

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/database/status');
      const data = await response.json();
      if (data.success) {
        setStatus(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch database status:', error);
    } finally {
      setLoading(false);
    }
  };

  const switchProvider = async (provider: string) => {
    setSwitching(true);
    try {
      const response = await fetch('/api/database/switch-provider', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provider }),
      });
      
      const data = await response.json();
      if (data.success) {
        await fetchStatus(); // Refresh status
      } else {
        alert(`Failed to switch provider: ${data.error}`);
      }
    } catch (error) {
      console.error('Failed to switch provider:', error);
      alert('Failed to switch provider');
    } finally {
      setSwitching(false);
    }
  };

  const triggerHealthCheck = async () => {
    try {
      await fetch('/api/database/health-check', { method: 'POST' });
      await fetchStatus(); // Refresh status
    } catch (error) {
      console.error('Failed to trigger health check:', error);
    }
  };

  useEffect(() => {
    fetchStatus();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800';
      case 'unhealthy':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'unhealthy':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Database className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatUptime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="w-8 h-8 animate-spin text-primary" />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-emerald-600 bg-clip-text text-transparent">
              🗄️ Database Dashboard
            </h1>
            <p className="text-muted-foreground mt-2">
              Monitor and manage database connections
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={fetchStatus} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={triggerHealthCheck} variant="outline" size="sm">
              <Activity className="w-4 h-4 mr-2" />
              Health Check
            </Button>
          </div>
        </div>

        {status && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Current Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Current Provider
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 mb-4">
                  <Badge variant="secondary" className="text-lg px-3 py-1">
                    {status.currentProvider}
                  </Badge>
                  {getStatusIcon(status.health.overall)}
                </div>
                <div className="text-sm text-muted-foreground">
                  <div>Connections: {status.connectionCount}</div>
                  <div>Uptime: {formatUptime(status.health.uptime)}</div>
                </div>
              </CardContent>
            </Card>

            {/* Health Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Health Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 mb-4">
                  <Badge className={getStatusColor(status.health.overall)}>
                    {status.health.overall.toUpperCase()}
                  </Badge>
                  {getStatusIcon(status.health.overall)}
                </div>
                {status.health.checks.length > 0 && (
                  <div className="text-sm text-muted-foreground">
                    <div>Response Time: {status.health.checks[0].responseTime}ms</div>
                    <div>Last Check: {new Date(status.health.checks[0].timestamp).toLocaleTimeString()}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Available Providers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="w-5 h-5" />
                  Available Providers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {['mysql', 'aiven-mysql', 'neon-postgresql', 'sqlite'].map((provider) => (
                    <div key={provider} className="flex items-center justify-between">
                      <span className="text-sm">{provider}</span>
                      <div className="flex items-center gap-2">
                        {status.currentProvider === provider ? (
                          <Badge variant="default" className="text-xs">Active</Badge>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => switchProvider(provider)}
                            disabled={switching}
                            className="text-xs px-2 py-1 h-6"
                          >
                            Switch
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Health Checks History */}
            {status.health.checks.length > 0 && (
              <Card className="md:col-span-2 lg:col-span-3">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    Recent Health Checks
                  </CardTitle>
                  <CardDescription>
                    Latest health check results for all providers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {status.health.checks.map((check, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getStatusIcon(check.status)}
                          <div>
                            <div className="font-medium">{check.provider}</div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(check.timestamp).toLocaleString()}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusColor(check.status)}>
                            {check.status}
                          </Badge>
                          <div className="text-sm text-muted-foreground mt-1">
                            {check.responseTime}ms
                          </div>
                          {check.error && (
                            <div className="text-xs text-red-600 mt-1 max-w-xs truncate">
                              {check.error}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {!status && (
          <Card>
            <CardContent className="text-center py-12">
              <AlertTriangle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Unable to load database status</h3>
              <p className="text-muted-foreground mb-4">
                There was an error connecting to the database API
              </p>
              <Button onClick={fetchStatus}>
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}
      </main>

      <Footer />
    </div>
  );
}
