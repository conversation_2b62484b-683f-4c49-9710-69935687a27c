import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import AssessmentRunner from "@/components/assessment/AssessmentRunner";
import AssessmentInfo from "@/components/assessment/AssessmentInfo";
import { ASSESSMENTS, AssessmentKey } from "@/components/assessment/assessments-data";
import { apiRequest } from "@/lib/queryClient";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { InfoIcon, ArrowLeft, Home } from "lucide-react";
import { Link } from "wouter";
import Navbar from "@/components/sections/Navbar";

// Import improved anime-style assessment images with proper Islamic guidelines
import dass42Image from "@assets/generated_images/Anime_DASS-42_female_fa2973cd.png";
import gseImage from "@assets/generated_images/Anime_male_self-efficacy_f185d7bb.png";
import mhkqImage from "@assets/generated_images/Anime_mental_health_knowledge_fde31e21.png";
import mspssImage from "@assets/generated_images/Anime_social_support_separate_932f0c05.png";
import pddImage from "@assets/generated_images/Anime_anti-stigma_separate_85bbbf6e.png";
import heroImage from "@assets/generated_images/Anime_female_student_assessment_dfd43af5.png";

const assessmentImages = {
  DASS42: dass42Image,
  GSE: gseImage,
  MHKQ: mhkqImage,
  MSPSS: mspssImage,
  PDD: pddImage,
};

const AssessmentsPage = () => {
  const [active, setActive] = useState<AssessmentKey | null>(null);
  type Latest = { percent: number; level: string; summary: string; completedAt: string };
  const [latest, setLatest] = useState<Record<AssessmentKey, Latest | null>>({} as any);
  const { session, signIn, signUp } = useAuth();
  const [authOpen, setAuthOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [syncLoading, setSyncLoading] = useState(false);
  const [authLoading, setAuthLoading] = useState(false);

  useEffect(() => {
    document.title = "Assessment | SantriMental";
    const meta = document.querySelector('meta[name="description"]');
    if (meta) meta.setAttribute("content", "Pilih dan isi kuisioner DASS-42, GSE, MHKQ, MSPSS, dan PDD secara in-app.");
    const link = document.querySelector('link[rel="canonical"]');
    if (!link) {
      const l = document.createElement('link');
      l.setAttribute('rel', 'canonical');
      l.setAttribute('href', window.location.origin + '/assessments');
      document.head.appendChild(l);
    }
  }, []);

  // Memuat hasil lokal saat inisialisasi dan ketika kembali dari runner ditangani di effect bawah ([active])

  useEffect(() => {
    if (active) return; // hanya muat ulang saat kembali ke daftar
    const data: Record<AssessmentKey, Latest | null> = {} as any;
    (Object.keys(ASSESSMENTS) as AssessmentKey[]).forEach((k) => {
      const raw = localStorage.getItem(`assessment_latest_${k}`);
      if (raw) {
        try {
          const j = JSON.parse(raw);
          data[k] = { percent: j.percent, level: j.level, summary: j.summary, completedAt: j.completedAt };
        } catch {
          data[k] = null;
        }
      } else {
        data[k] = null;
      }
    });
    setLatest(data);
  }, [active]);

  const handleSync = async () => {
    const uid = session?.user?.id;
    if (!uid) {
      setAuthOpen(true);
      toast({ title: "Masuk diperlukan", description: "Silakan masuk untuk menyinkronkan data." });
      return;
    }
    setSyncLoading(true);
    try {
      const assessmentPromises = (Object.keys(ASSESSMENTS) as AssessmentKey[])
        .map(async (k) => {
          const raw = localStorage.getItem(`assessment_latest_${k}`);
          if (!raw) return null;
          try {
            const j = JSON.parse(raw);
            return await apiRequest('/api/assessments', {
              method: 'POST',
              body: JSON.stringify({
                jenisAssessment: k,
                hasilJawaban: j, // ringkasan lokal
                skorTotal: typeof j.total === 'number' ? j.total : null,
                interpretasi: j.summary ?? null,
                tanggalAssessment: j.completedAt ?? new Date().toISOString(),
              }),
            });
          } catch {
            return null;
          }
        })
        .filter(Boolean);

      if (!assessmentPromises.length) {
        toast({ title: "Tidak ada data", description: "Belum ada hasil lokal untuk disinkronkan." });
      } else {
        await Promise.all(assessmentPromises);
        toast({ title: "Sinkron berhasil", description: `${assessmentPromises.length} hasil tersimpan ke database.` });
      }
    } catch (e: any) {
      toast({ variant: 'destructive', title: 'Gagal sinkron', description: e?.message ?? 'Terjadi kesalahan.' });
    } finally {
      setSyncLoading(false);
    }
  };

  const handleAuth = async (isSignUp: boolean) => {
    setAuthLoading(true);
    try {
      const result = isSignUp ? await signUp(email, password) : await signIn(email, password);
      if (result.error) {
        toast({ variant: 'destructive', title: 'Gagal', description: result.error.message });
      } else {
        toast({ title: 'Berhasil', description: isSignUp ? 'Akun berhasil dibuat' : 'Berhasil masuk' });
        setAuthOpen(false);
        setEmail('');
        setPassword('');
      }
    } catch (e: any) {
      toast({ variant: 'destructive', title: 'Kesalahan', description: e?.message ?? 'Terjadi kesalahan.' });
    } finally {
      setAuthLoading(false);
    }
  };

  if (active) {
    return <AssessmentRunner keyId={active} onExit={() => setActive(null)} />;
  }

  return (
    <>
      <Navbar />
      <section className="container mx-auto px-4 py-6 max-w-screen-sm">
        {/* Navigation */}
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="gap-2 p-2" data-testid="button-back-home">
              <ArrowLeft size={16} />
              Kembali ke Beranda
            </Button>
          </Link>
        </div>

        {/* Hero Section */}
        <div className="mb-8">
          <div className="w-full h-48 rounded-lg overflow-hidden border mb-4">
            <img 
              src={heroImage}
              alt="Assessment Mental Health dengan teknologi neuroscience dan tema pesantren modern"
              className="w-full h-full object-cover"
            />
          </div>
          <h1 className="text-2xl font-semibold">Pilih Assessment Kesehatan Mental</h1>
          <p className="text-muted-foreground mt-1">Instrumen tervalidasi berdasarkan standar Kementerian Kesehatan RI untuk kesehatan mental santri di era digital.</p>
        </div>

      <div className="mt-4 flex items-center justify-between">
        <span className="text-xs text-muted-foreground">Simpan dan sinkronkan hasil Anda.</span>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleSync} disabled={syncLoading}>
            {session ? (syncLoading ? 'Menyinkronkan...' : 'Sinkronkan ke Database') : 'Masuk untuk Sinkronkan'}
          </Button>
          {!session && (
            <Dialog open={authOpen} onOpenChange={setAuthOpen}>
              <DialogTrigger asChild>
                <Button variant="secondary">Masuk / Daftar</Button>
              </DialogTrigger>
              <DialogContent className="w-[95vw] max-w-md mx-auto">
                <DialogHeader>
                  <DialogTitle className="text-lg">Masuk atau Daftar</DialogTitle>
                  <DialogDescription className="text-sm">Gunakan email dan kata sandi untuk mengakses akun Anda.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-2">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      value={email} 
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium">Kata Sandi</Label>
                    <Input 
                      id="password" 
                      type="password" 
                      value={password} 
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full"
                      placeholder="Masukkan kata sandi"
                    />
                  </div>
                </div>
                <DialogFooter className="flex flex-col sm:flex-row gap-2 pt-2">
                  <Button
                    variant="outline"
                    onClick={() => handleAuth(true)}
                    disabled={authLoading}
                    className="w-full sm:w-auto"
                  >
                    {authLoading ? 'Mendaftar...' : 'Daftar'}
                  </Button>
                  <Button
                    onClick={() => handleAuth(false)}
                    disabled={authLoading}
                    className="w-full sm:w-auto"
                  >
                    {authLoading ? 'Masuk...' : 'Masuk'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      <div className="mt-6 grid gap-6">
        {Object.values(ASSESSMENTS).map((it) => {
          const res = latest[it.key as AssessmentKey];
          const assessmentImage = assessmentImages[it.key as AssessmentKey];
          
          return (
            <Card key={it.key} className="relative flex flex-col overflow-hidden">
              {res && (
                <div className="absolute right-3 top-3 z-10">
                  <span className="rounded-full bg-primary text-primary-foreground text-xs px-2 py-1 shadow">
                    {Math.round(res.percent)}% • {res.level}
                  </span>
                </div>
              )}
              
              {/* Assessment Image */}
              <div className="w-full h-40 overflow-hidden">
                <img 
                  src={assessmentImage} 
                  alt={it.title}
                  className="w-full h-full object-cover"
                />
              </div>
              
              <CardHeader className="pb-2">
                <CardTitle className="text-base">{it.title}</CardTitle>
              </CardHeader>
              
              <CardContent className="flex flex-col gap-3 flex-grow">
                <p className="text-sm text-muted-foreground leading-relaxed">{it.description}</p>
                
                {res && (
                  <div className="space-y-2">
                    <Progress value={res.percent} className="h-2" />
                    <p className="text-xs text-muted-foreground">Terakhir: {res.summary}</p>
                  </div>
                )}
                
                <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                  {it.scaleNote}
                </div>
                
                <div className="flex flex-col sm:flex-row gap-2 mt-auto">
                  <AssessmentInfo 
                    assessmentKey={it.key as AssessmentKey}
                    trigger={
                      <Button variant="outline" size="sm" className="gap-2 flex-1">
                        <InfoIcon className="h-4 w-4" />
                        Pelajari Lebih Lanjut
                      </Button>
                    }
                  />
                  <Button 
                    onClick={() => setActive(it.key as AssessmentKey)}
                    className="flex-1"
                  >
                    {res ? "Ulangi" : "Mulai"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      <div className="mt-8">
        <h2 className="text-lg font-semibold">Edukasi Kesehatan Mental</h2>
        <p className="text-sm text-muted-foreground">Pelajari lebih lanjut melalui modul, video, dan game.</p>
        <div className="mt-3 flex flex-wrap gap-2">
          <a className="underline text-sm" href="https://drive.google.com/file/d/1J9Me3jFdIvwWZFfCzgyoCrHbEk2/view?usp=sharing" target="_blank">E-Modul Psikoedukasi</a>
          <a className="underline text-sm" href="https://mizu-izumi.itch.io/gen-zas" target="_blank">Game Anti-Bullying</a>
        </div>
      </div>
    </section>
    </>
  );
};

export default AssessmentsPage;
