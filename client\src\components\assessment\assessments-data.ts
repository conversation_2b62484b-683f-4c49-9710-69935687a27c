export type AssessmentKey = "DASS42" | "GSE" | "MHKQ" | "MSPSS" | "PDD";

export type Question = {
  id: string;
  text: string;
  type: "likert" | "boolean";
  options?: { value: number; label: string }[]; // for likert
  correct?: boolean; // for boolean (true=Benar)
  reverse?: boolean; // for reverse scoring (Likert)
  domain?: string; // subscale/domain
};

export type AssessmentConfig = {
  key: AssessmentKey;
  title: string;
  description: string;
  scaleNote?: string;
  questions: Question[];
};

const DASS_OPTIONS = [
  { value: 0, label: "Tidak Pernah" },
  { value: 1, label: "Kadang-kadang" },
  { value: 2, label: "Sering" },
  { value: 3, label: "Sangat Sering" },
];

const GSE_OPTIONS = [
  { value: 1, label: "Sangat Tidak Benar" },
  { value: 2, label: "Tidak Benar" },
  { value: 3, label: "Benar" },
  { value: 4, label: "Sangat Benar" },
];

const MSPSS_OPTIONS = [
  { value: 1, label: "Sangat Tidak Setuju" },
  { value: 2, label: "Tidak Setuju" },
  { value: 3, label: "Agak Tidak Setuju" },
  { value: 4, label: "Netral" },
  { value: 5, label: "Agak Setuju" },
  { value: 6, label: "Setuju" },
  { value: 7, label: "Sangat Setuju" },
];

const PDD_OPTIONS = [
  { value: 1, label: "Sangat Tidak Setuju" },
  { value: 2, label: "Tidak Setuju" },
  { value: 3, label: "Ragu-ragu" },
  { value: 4, label: "Setuju" },
  { value: 5, label: "Sangat Setuju" },
];

export const ASSESSMENTS: Record<AssessmentKey, AssessmentConfig> = {
  DASS42: {
    key: "DASS42",
    title: "DASS-42 (Depresi, Kecemasan, Stres)",
    description:
      "Skrining gejala depresi, kecemasan, dan stres. Versi demo (contoh butir).",
    scaleNote: "Likert 0–3: Tidak Pernah – Sangat Sering",
    questions: [
      // Contoh 6 butir (2 per domain). Versi penuh memiliki 42 butir.
      { id: "d1", text: "Saya sulit untuk rileks.", type: "likert", options: DASS_OPTIONS, domain: "Stress" },
      { id: "d2", text: "Saya merasa tidak ada harapan.", type: "likert", options: DASS_OPTIONS, domain: "Depresi" },
      { id: "d3", text: "Saya merasa gugup atau cemas tanpa alasan jelas.", type: "likert", options: DASS_OPTIONS, domain: "Kecemasan" },
      { id: "d4", text: "Saya mudah tersinggung atau marah.", type: "likert", options: DASS_OPTIONS, domain: "Stress" },
      { id: "d5", text: "Saya merasa sedih dan murung.", type: "likert", options: DASS_OPTIONS, domain: "Depresi" },
      { id: "d6", text: "Saya mengalami jantung berdebar atau berkeringat.", type: "likert", options: DASS_OPTIONS, domain: "Kecemasan" },
    ],
  },
  GSE: {
    key: "GSE",
    title: "GSE (General Self-Efficacy)",
    description:
      "Mengukur keyakinan diri umum dalam menghadapi tantangan. Versi demo (contoh butir).",
    scaleNote: "Likert 1–4: Sangat Tidak Benar – Sangat Benar",
    questions: [
      { id: "g1", text: "Saya dapat selalu menemukan cara untuk mengatasi masalah sulit.", type: "likert", options: GSE_OPTIONS },
      { id: "g2", text: "Jika saya gigih, saya bisa mencapai tujuan saya.", type: "likert", options: GSE_OPTIONS },
      { id: "g3", text: "Saya tetap tenang ketika menghadapi tantangan tak terduga.", type: "likert", options: GSE_OPTIONS },
    ],
  },
  MHKQ: {
    key: "MHKQ",
    title: "MHKQ (Mental Health Knowledge)",
    description:
      "Menilai pengetahuan kesehatan mental (benar/salah). Versi demo (contoh butir).",
    scaleNote: "Jawaban Benar/Salah",
    questions: [
      { id: "m1", text: "Gangguan depresi hanya disebabkan oleh kelemahan iman.", type: "boolean", correct: false },
      { id: "m2", text: "Orang dengan gangguan cemas dapat pulih dengan perawatan yang tepat.", type: "boolean", correct: true },
      { id: "m3", text: "Stigma dapat menghambat orang mencari bantuan profesional.", type: "boolean", correct: true },
    ],
  },
  MSPSS: {
    key: "MSPSS",
    title: "MSCS/MSPSS (Dukungan Sosial)",
    description:
      "Persepsi dukungan dari keluarga, teman, dan figur signifikan. Versi demo (contoh butir).",
    scaleNote: "Likert 1–7: Sangat Tidak Setuju – Sangat Setuju",
    questions: [
      { id: "s1", text: "Ada seseorang yang benar-benar peduli pada perasaan saya.", type: "likert", options: MSPSS_OPTIONS, domain: "Significant Other" },
      { id: "s2", text: "Keluarga saya berusaha membantu saya.", type: "likert", options: MSPSS_OPTIONS, domain: "Keluarga" },
      { id: "s3", text: "Saya memiliki teman yang bisa saya andalkan.", type: "likert", options: MSPSS_OPTIONS, domain: "Teman" },
      { id: "s4", text: "Saya dapat berbagi kegembiraan dan kesedihan dengan pasangan/figur penting.", type: "likert", options: MSPSS_OPTIONS, domain: "Significant Other" },
    ],
  },
  PDD: {
    key: "PDD",
    title: "PDD (Perceived Devaluation-Discrimination)",
    description:
      "Persepsi stigma dan diskriminasi terhadap masalah kesehatan mental. Versi demo (contoh butir).",
    scaleNote: "Likert 1–5: Sangat Tidak Setuju – Sangat Setuju (beberapa butir dibalik)",
    questions: [
      { id: "p1", text: "Orang dengan masalah kesehatan mental cenderung dianggap rendah oleh masyarakat.", type: "likert", options: PDD_OPTIONS },
      { id: "p2", text: "Sebagian besar orang akan menerima mereka yang pernah dirawat karena gangguan mental.", type: "likert", options: PDD_OPTIONS, reverse: true },
      { id: "p3", text: "Teman akan menjauh dari seseorang dengan riwayat gangguan mental.", type: "likert", options: PDD_OPTIONS },
      { id: "p4", text: "Perusahaan bersedia mempekerjakan penyintas gangguan mental.", type: "likert", options: PDD_OPTIONS, reverse: true },
    ],
  },
};

export function scoreAssessment(key: AssessmentKey, answers: Record<string, number | boolean>) {
  const cfg = ASSESSMENTS[key];
  let total = 0;
  const domains: Record<string, number> = {};
  cfg.questions.forEach((q) => {
    if (q.type === "boolean") {
      const val = answers[q.id] as boolean | undefined;
      if (typeof val === "boolean" && typeof q.correct === "boolean") {
        total += val === q.correct ? 1 : 0;
        if (q.domain) domains[q.domain] = (domains[q.domain] || 0) + (val === q.correct ? 1 : 0);
      }
    } else if (q.options) {
      const raw = answers[q.id] as number | undefined;
      const vals = q.options.map((o) => o.value);
      const min = Math.min(...vals);
      const max = Math.max(...vals);
      if (typeof raw === "number") {
        const v = q.reverse ? (min + max - raw) : raw;
        total += v;
        if (q.domain) domains[q.domain] = (domains[q.domain] || 0) + v;
      }
    }
  });
  return { total, domains };
}

export function getMaxTotals(key: AssessmentKey) {
  const cfg = ASSESSMENTS[key];
  let maxTotal = 0;
  const domainMax: Record<string, number> = {};
  cfg.questions.forEach((q) => {
    if (q.type === "boolean") {
      maxTotal += 1;
      if (q.domain) domainMax[q.domain] = (domainMax[q.domain] || 0) + 1;
    } else if (q.options) {
      const vals = q.options.map((o) => o.value);
      const max = Math.max(...vals);
      const min = Math.min(...vals);
      maxTotal += max; // reverse tidak mengubah maksimum per butir
      if (q.domain) domainMax[q.domain] = (domainMax[q.domain] || 0) + max;
    }
  });
  return { maxTotal, domainMax };
}

export type Analysis = {
  percent: number;
  level: string;
  summary: string;
  domainLevels?: Record<string, string>;
};

function levelByPercent(p: number, bands: number[], labels: string[]) {
  for (let i = 0; i < bands.length; i++) {
    if (p < bands[i]) return labels[i];
  }
  return labels[labels.length - 1];
}

export function analyzeAssessment(
  key: AssessmentKey,
  score: { total: number; domains: Record<string, number> },
  max: { maxTotal: number; domainMax: Record<string, number> }
): Analysis {
  const percent = max.maxTotal > 0 ? (score.total / max.maxTotal) * 100 : 0;
  let level = "";
  let summary = "";
  const domainLevels: Record<string, string> = {};

  if (key === "DASS42") {
    // Skor lebih tinggi = gejala lebih berat
    const bands = [20, 40, 60, 80];
    const labels = ["Normal", "Mild", "Moderate", "Severe", "Extremely Severe"];
    // Domain
    Object.keys(max.domainMax).forEach((d) => {
      const dp = (score.domains[d] || 0) / (max.domainMax[d] || 1) * 100;
      domainLevels[d] = levelByPercent(dp, bands, labels);
    });
    // Overall = tingkat terberat dari domain
    const idx = Object.values(domainLevels).reduce((acc, lv) => {
      const i = labels.indexOf(lv);
      return Math.max(acc, i);
    }, 0);
    level = labels[idx];
    summary = `Indikasi: ${Object.entries(domainLevels).map(([d, lv]) => `${d} ${lv}`).join(", ")}`;
  } else if (key === "GSE") {
    const bands = [40, 70];
    const labels = ["Rendah", "Sedang", "Tinggi"];
    level = levelByPercent(percent, bands, labels);
    summary = "Keyakinan diri umum " + level.toLowerCase();
  } else if (key === "MHKQ") {
    const bands = [50, 75];
    const labels = ["Rendah", "Sedang", "Tinggi"];
    level = levelByPercent(percent, bands, labels);
    summary = "Pengetahuan kesehatan mental " + level.toLowerCase();
  } else if (key === "MSPSS") {
    const bands = [40, 70];
    const labels = ["Rendah", "Sedang", "Tinggi"];
    Object.keys(max.domainMax).forEach((d) => {
      const dp = (score.domains[d] || 0) / (max.domainMax[d] || 1) * 100;
      domainLevels[d] = levelByPercent(dp, bands, labels);
    });
    level = levelByPercent(percent, bands, labels);
    summary = `Dukungan sosial: ${Object.entries(domainLevels).map(([d, lv]) => `${d} ${lv}`).join(", ")}`;
  } else if (key === "PDD") {
    // Skor lebih tinggi = stigma lebih tinggi
    const bands = [33, 66];
    const labels = ["Rendah", "Sedang", "Tinggi"];
    level = levelByPercent(percent, bands, labels);
    summary = "Persepsi stigma " + level.toLowerCase();
  }

  return { percent, level, summary, domainLevels: Object.keys(domainLevels).length ? domainLevels : undefined };
}

