import fetch from 'node-fetch';

const testConnection = async () => {
  console.log('Testing Aiven MySQL connection...');
  try {
    const response = await fetch('http://localhost:5000/api/database/test-connections');
    const result = await response.json();
    console.log('Test Result:');
    console.log(JSON.stringify(result, null, 2));

    if (result.success && result.data.results['aiven-mysql']) {
      console.log('\n✅ Aiven MySQL connection successful!');
    } else {
      console.error('\n❌ Aiven MySQL connection failed.');
      if (result.error) {
        console.error('Error details:', result.error);
      }
    }
  } catch (error) {
    console.error('An error occurred while testing the connection:', error);
  }
};

testConnection();
