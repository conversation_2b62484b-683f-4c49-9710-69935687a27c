import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { InfoIcon } from "lucide-react";

// Import improved anime-style feature images with futuristic pesantren tech
import assessmentImage from "@assets/generated_images/Anime_digital_platform_separate_8fac5857.png";
import educationImage from "@assets/generated_images/Anime_educational_resources_tech_0a3c4da5.png";
import gamesImage from "@assets/generated_images/Anime_interactive_games_futuristic_f1f714b4.png";
import analyticsImage from "@assets/generated_images/Anime_analytics_futuristic_tech_4639e93e.png";

type FeatureKey = "assessment" | "therapeutic" | "education" | "analytics";

type FeatureInfoType = {
  title: string;
  image: string;
  description: string;
  features: string[];
  benefits: string[];
  usage: string;
  note?: string;
};

const featureInfo: Record<FeatureKey, FeatureInfoType> = {
  assessment: {
    title: "Assessment Terstruktur",
    image: assessmentImage,
    description: "Platform SantriMental menyediakan berbagai alat assessment psikologis yang telah tervalidasi secara ilmiah untuk mengukur kesehatan mental santri.",
    features: [
      "DASS-42: Mengukur tingkat depresi, kecemasan, dan stres",
      "GSE: Menilai keyakinan diri dalam menghadapi tantangan",
      "MHKQ: Menguji pengetahuan tentang kesehatan mental",
      "MSPSS: Mengukur persepsi dukungan sosial",
      "PDD: Menilai persepsi stigma terhadap gangguan mental"
    ],
    benefits: [
      "Assessment yang valid dan reliabel",
      "Hasil real-time dengan interpretasi yang mudah dipahami",
      "Dapat digunakan untuk skrining awal",
      "Membantu identifikasi dini masalah kesehatan mental"
    ],
    usage: "Santri dapat mengakses assessment kapan saja melalui platform digital ini. Setiap assessment dilengkapi dengan panduan yang jelas dan hasil yang dapat dipahami dengan mudah."
  },
  therapeutic: {
    title: "Rekomendasi Terapeutik",
    image: educationImage,
    description: "Berdasarkan hasil assessment, platform memberikan rekomendasi perawatan diri dan modifikasi perilaku yang disesuaikan dengan kebutuhan individual santri.",
    features: [
      "Rekomendasi perawatan diri berbasis hasil assessment",
      "Panduan modifikasi perilaku yang praktis",
      "Strategi coping yang islami dan sesuai budaya pesantren",
      "Rujukan ke sumber daya yang tepat"
    ],
    benefits: [
      "Intervensi dini untuk mencegah masalah yang lebih serius",
      "Pendekatan yang sesuai dengan nilai-nilai Islam",
      "Mudah diterapkan dalam kehidupan sehari-hari",
      "Membangun resiliensi dan self-efficacy"
    ],
    usage: "Setelah menyelesaikan assessment, santri akan mendapatkan rekomendasi yang spesifik dan actionable untuk meningkatkan kesejahteraan mental mereka."
  },
  education: {
    title: "Edukasi Interaktif",
    image: gamesImage,
    description: "Platform menyediakan berbagai material edukasi interaktif yang dirancang khusus untuk meningkatkan literasi kesehatan mental di lingkungan pesantren.",
    features: [
      "E-modul psikoedukasi yang komprehensif",
      "Video pembelajaran yang engaging",
      "Game anti-bullying (GEN ZAS) untuk pencegahan",
      "Materi yang disesuaikan dengan konteks pesantren"
    ],
    benefits: [
      "Meningkatkan awareness tentang kesehatan mental",
      "Mengurangi stigma melalui edukasi",
      "Pembelajaran yang fun dan interaktif",
      "Akses 24/7 ke material pembelajaran"
    ],
    usage: "Santri dapat mengakses berbagai material edukasi melalui platform, baik untuk pembelajaran mandiri maupun sebagai bagian dari program edukasi di pesantren."
  },
  analytics: {
    title: "Analytics & Riwayat",
    image: analyticsImage,
    description: "Fitur tracking dan analytics yang memungkinkan santri dan pengasuh untuk melihat perkembangan kesehatan mental dari waktu ke waktu.",
    features: [
      "Dashboard progres kesehatan mental",
      "Riwayat hasil assessment yang lengkap",
      "Visualisasi data yang mudah dipahami",
      "Trend analysis untuk identifikasi pola"
    ],
    benefits: [
      "Monitoring progres jangka panjang",
      "Identifikasi pola dan trigger",
      "Motivasi untuk konsistensi perawatan diri",
      "Data untuk evaluasi program pesantren"
    ],
    usage: "Fitur ini akan membantu santri memahami perjalanan kesehatan mental mereka dan memberikan insight berharga untuk pengasuh dalam merancang program yang lebih efektif.",
    note: "Fitur ini sedang dalam tahap pengembangan dan akan segera tersedia."
  }
};

type Props = {
  featureKey: FeatureKey;
  trigger?: React.ReactNode;
};

const FeatureInfo = ({ featureKey, trigger }: Props) => {
  const info = featureInfo[featureKey];
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="gap-2">
            <InfoIcon className="h-4 w-4" />
            Pelajari Lebih Lanjut
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] overflow-auto" aria-describedby="feature-description">
        <DialogHeader>
          <DialogTitle className="text-lg">{info.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="w-full h-48 rounded-lg overflow-hidden border">
            <img 
              src={info.image} 
              alt={info.title}
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="space-y-4">
            <section id="feature-description">
              <h3 className="font-semibold text-base mb-2">Tentang Fitur Ini</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.description}</p>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Fitur Utama</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                {info.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-primary mt-1">•</span>
                    <span className="leading-relaxed">{feature}</span>
                  </li>
                ))}
              </ul>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Manfaat</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                {info.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-green-500 mt-1">✓</span>
                    <span className="leading-relaxed">{benefit}</span>
                  </li>
                ))}
              </ul>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Cara Penggunaan</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.usage}</p>
            </section>
            
            {info.note && (
              <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Catatan:</strong> {info.note}
                </p>
              </div>
            )}
            
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Komitmen Kami:</strong> Semua fitur dirancang dengan mempertimbangkan nilai-nilai Islam dan budaya pesantren, 
                serta berbasis pada penelitian ilmiah terkini dalam bidang psikologi dan kesehatan mental.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FeatureInfo;