#!/usr/bin/env node

/**
 * 🔍 Simple Server Test
 * 
 * Tests if server can start and basic database connection works
 */

import "dotenv/config";
import express from "express";

const app = express();

// Basic middleware
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    message: 'Server is running'
  });
});

// Database config test endpoint
app.get('/api/database/config', (req, res) => {
  try {
    // Simple database config detection
    const config = {
      provider: process.env.DATABASE_PROVIDER || 'auto-detect',
      hasAivenUrl: !!process.env.AIVEN_MYSQL_URL,
      hasNeonUrl: !!process.env.NEON_DATABASE_URL,
      hasLocalUrl: !!process.env.DATABASE_URL,
      jwtSecret: !!process.env.JWT_SECRET
    };

    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start server
const port = 5000;
const server = app.listen(port, 'localhost', () => {
  console.log(`🚀 Simple test server running on http://localhost:${port}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   GET /health - Health check`);
  console.log(`   GET /api/database/config - Database config test`);
  
  // Test database config
  try {
    console.log(`🗄️ Environment variables check:`);
    console.log(`   DATABASE_PROVIDER: ${process.env.DATABASE_PROVIDER || 'not set'}`);
    console.log(`   AIVEN_MYSQL_URL: ${process.env.AIVEN_MYSQL_URL ? 'configured' : 'not set'}`);
    console.log(`   NEON_DATABASE_URL: ${process.env.NEON_DATABASE_URL ? 'configured' : 'not set'}`);
    console.log(`   DATABASE_URL: ${process.env.DATABASE_URL ? 'configured' : 'not set'}`);
    console.log(`   JWT_SECRET: ${process.env.JWT_SECRET ? 'configured' : 'not set'}`);
  } catch (error) {
    console.error(`❌ Database config error:`, error.message);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
