import { connectionManager } from './connection-manager';
import { DatabaseProvider } from '../config/database';

export interface HealthCheck {
  provider: DatabaseProvider;
  status: 'healthy' | 'unhealthy' | 'unknown';
  responseTime: number;
  error?: string;
  timestamp: Date;
}

export interface HealthReport {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  checks: HealthCheck[];
  currentProvider: DatabaseProvider | null;
  uptime: number;
  timestamp: Date;
}

export class DatabaseHealthMonitor {
  private static instance: DatabaseHealthMonitor;
  private healthChecks: Map<DatabaseProvider, HealthCheck> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private startTime: Date = new Date();

  private constructor() {}

  static getInstance(): DatabaseHealthMonitor {
    if (!DatabaseHealthMonitor.instance) {
      DatabaseHealthMonitor.instance = new DatabaseHealthMonitor();
    }
    return DatabaseHealthMonitor.instance;
  }

  startMonitoring(intervalMs: number = 30000): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    console.log(`🔍 Starting database health monitoring (interval: ${intervalMs}ms)`);
    
    // Initial check
    this.performHealthCheck();

    // Periodic checks
    this.monitoringInterval = setInterval(() => {
      this.performHealthCheck();
    }, intervalMs);
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('🛑 Database health monitoring stopped');
    }
  }

  async performHealthCheck(): Promise<void> {
    const currentProvider = connectionManager.getCurrentProvider();
    
    if (!currentProvider) {
      console.warn('⚠️ No database provider initialized for health check');
      return;
    }

    try {
      const connection = await connectionManager.getConnection();
      const startTime = Date.now();
      
      // Perform basic connectivity test
      let isHealthy = false;
      let error: string | undefined;

      try {
        if (connection.testConnection) {
          isHealthy = await connection.testConnection();
        } else if (connection.db) {
          // For legacy connections, try a simple query
          await this.performSimpleQuery(connection.db, currentProvider);
          isHealthy = true;
        }
      } catch (testError: any) {
        error = testError.message;
        isHealthy = false;
      }

      const responseTime = Date.now() - startTime;

      const healthCheck: HealthCheck = {
        provider: currentProvider,
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        error,
        timestamp: new Date()
      };

      this.healthChecks.set(currentProvider, healthCheck);

      if (!isHealthy) {
        console.error(`❌ Health check failed for ${currentProvider}: ${error}`);
        
        // Attempt failover if configured
        await this.attemptFailover();
      } else {
        console.log(`✅ Health check passed for ${currentProvider} (${responseTime}ms)`);
      }

    } catch (error: any) {
      console.error('❌ Health check error:', error);
      
      const healthCheck: HealthCheck = {
        provider: currentProvider,
        status: 'unhealthy',
        responseTime: -1,
        error: error.message,
        timestamp: new Date()
      };

      this.healthChecks.set(currentProvider, healthCheck);
    }
  }

  private async performSimpleQuery(db: any, provider: DatabaseProvider): Promise<void> {
    switch (provider) {
      case 'mysql':
      case 'aiven-mysql':
        await db.execute('SELECT 1');
        break;
      case 'postgresql':
      case 'neon-postgresql':
        await db.execute('SELECT 1');
        break;
      case 'sqlite':
        await db.run('SELECT 1');
        break;
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  private async attemptFailover(): Promise<void> {
    const currentProvider = connectionManager.getCurrentProvider();
    console.log(`🔄 Attempting failover from ${currentProvider}...`);

    // Define failover priority
    const failoverOrder: DatabaseProvider[] = ['aiven-mysql', 'mysql', 'neon-postgresql', 'sqlite'];
    
    for (const provider of failoverOrder) {
      if (provider === currentProvider) continue;

      try {
        await connectionManager.switchProvider(provider);
        console.log(`✅ Failover successful: switched to ${provider}`);
        return;
      } catch (error) {
        console.error(`❌ Failover to ${provider} failed:`, error);
      }
    }

    console.error('❌ All failover attempts failed');
  }

  getHealthReport(): HealthReport {
    const checks = Array.from(this.healthChecks.values());
    const currentProvider = connectionManager.getCurrentProvider();
    
    let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (checks.length === 0) {
      overall = 'unknown' as any;
    } else {
      const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;
      const totalCount = checks.length;
      
      if (unhealthyCount === totalCount) {
        overall = 'unhealthy';
      } else if (unhealthyCount > 0) {
        overall = 'degraded';
      }
    }

    return {
      overall,
      checks,
      currentProvider,
      uptime: Date.now() - this.startTime.getTime(),
      timestamp: new Date()
    };
  }

  getLatestCheck(provider?: DatabaseProvider): HealthCheck | null {
    const targetProvider = provider || connectionManager.getCurrentProvider();
    
    if (!targetProvider) {
      return null;
    }

    return this.healthChecks.get(targetProvider) || null;
  }

  clearHistory(): void {
    this.healthChecks.clear();
    console.log('🧹 Health check history cleared');
  }

  // Webhook support for external monitoring
  async notifyWebhook(webhookUrl: string, report: HealthReport): Promise<void> {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service: 'santrimental-database',
          status: report.overall,
          timestamp: report.timestamp,
          details: report
        })
      });

      if (!response.ok) {
        throw new Error(`Webhook failed: ${response.status}`);
      }

      console.log('📡 Health report sent to webhook');
    } catch (error) {
      console.error('❌ Webhook notification failed:', error);
    }
  }
}

// Export singleton instance
export const healthMonitor = DatabaseHealthMonitor.getInstance();
