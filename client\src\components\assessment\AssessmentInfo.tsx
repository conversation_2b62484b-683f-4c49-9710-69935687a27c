import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { InfoIcon } from "lucide-react";
import { AssessmentKey } from "./assessments-data";

// Import improved anime-style generated images with proper Islamic guidelines
import dass42Image from "@assets/generated_images/Anime_DASS-42_female_fa2973cd.png";
import gseImage from "@assets/generated_images/Anime_male_self-efficacy_f185d7bb.png";
import mhkqImage from "@assets/generated_images/Anime_mental_health_knowledge_fde31e21.png";
import mspssImage from "@assets/generated_images/Anime_social_support_separate_932f0c05.png";
import pddImage from "@assets/generated_images/Anime_anti-stigma_separate_85bbbf6e.png";

const assessmentInfo = {
  DASS42: {
    title: "DASS-42 (Depression, Anxiety, Stress Scale)",
    image: dass42Image,
    description: "DASS-42 adalah instrumen skrining yang telah tervalidasi secara internasional dan diadaptasi sesuai norma budaya Indonesia untuk mengukur tingkat depresi, kecemasan, dan stres berdasarkan pengalaman 7 hari terakhir. Instrumen ini mengikuti standar diagnostik ICD-11 WHO dan pedoman Kementerian Kesehatan RI.",
    purpose: "Deteksi dini gangguan mood dan kecemasan untuk rujukan tepat waktu ke profesional kesehatan mental berlisensi, mencegah perkembangan gangguan yang lebih berat.",
    whatItMeasures: [
      "Depresi: Mood rendah persistens, anhedonia, perasaan tidak berharga, gangguan kognitif, gejala neurovegetatif (tidur, nafsu makan)",
      "Kecemasan: Gejala somatik (tremor, berkeringat), ketegangan otot, kekhawatiran berlebihan, serangan panik, agorafobia", 
      "Stres: Aktivasi sistem simpatik, iritabilitas, kesulitan konsentrasi, intoleransi terhadap interupsi, agitasi psikomotor"
    ],
    howToUse: "Baca setiap item dengan cermat dan pilih respons yang paling sesuai dengan kondisi Anda dalam 7 hari terakhir menggunakan skala Likert 0-3: 0=Tidak Pernah, 1=Kadang-kadang, 2=Lumayan Sering, 3=Sangat Sering.",
    interpretation: "Cut-off score berdasarkan penelitian psikometrik: Depresi (Normal 0-9, Ringan 10-13, Sedang 14-20, Berat 21-27, Sangat Berat 28+), Kecemasan (Normal 0-7, Ringan 8-9, Sedang 10-14, Berat 15-19, Sangat Berat 20+), Stres (Normal 0-14, Ringan 15-18, Sedang 19-25, Berat 26-33, Sangat Berat 34+).",
    importance: "Screening rutin sesuai rekomendasi WHO untuk deteksi dini gangguan mental pada populasi berisiko tinggi seperti remaja dan dewasa muda di lingkungan pendidikan.",
    clinicalReferences: [
      "Kementerian Kesehatan RI (2022): Pedoman Nasional Pelayanan Kesehatan Mental dan Narkoba",
      "Lovibond & Lovibond (1995): Manual for Depression Anxiety Stress Scales (2nd Ed.)",
      "WHO (2019): ICD-11 Classification of Mental, Behavioural and Neurodevelopmental Disorders"
    ]
  },
  GSE: {
    title: "GSE (General Self-Efficacy Scale)",
    image: gseImage,
    description: "GSE adalah instrumen psikometrik yang mengukur keyakinan individu terhadap kemampuan diri dalam mengatasi tuntutan hidup dan situasi stres. Dikembangkan berdasarkan teori kognitif-sosial Albert Bandura dan telah diadaptasi untuk populasi Indonesia dengan validitas tinggi.",
    purpose: "Mengidentifikasi tingkat self-efficacy sebagai faktor protektif kesehatan mental, predictor resiliensi, dan kemampuan coping adaptif terhadap stressor psikososial.",
    whatItMeasures: [
      "Keyakinan diri dalam problem-solving dan decision-making",
      "Perceived control dan sense of agency dalam situasi challenging",
      "Optimisme realistis dan persistence dalam goal-directed behavior",
      "Kemampuan adaptasi terhadap perubahan dan adversity"
    ],
    howToUse: "Evaluasi setiap pernyataan menggunakan skala Likert 4-point: 1=Tidak Benar Sama Sekali, 2=Hampir Tidak Benar, 3=Agak Benar, 4=Sangat Benar. Total skor berkisar 10-40.",
    interpretation: "Interpretasi berdasarkan norma populasi Indonesia: Rendah (10-25), Sedang (26-32), Tinggi (33-40). Skor tinggi berkorelasi positif dengan well-being, academic performance, dan resiliensi terhadap gangguan mental.",
    importance: "Self-efficacy merupakan mediator penting antara stressor dan psychological distress. Individu dengan GSE tinggi menunjukkan risk yang lebih rendah untuk mengalami depresi, kecemasan, dan burnout.",
    clinicalReferences: [
      "Schwarzer & Jerusalem (1995): General Self-Efficacy Scale Development",
      "Luszczynska et al. (2005): Cross-cultural validation GSE dalam 25 negara",
      "Kementerian Kesehatan RI (2020): Panduan Assessment Psikologis untuk Tenaga Kesehatan"
    ]
  },
  MHKQ: {
    title: "MHKQ (Mental Health Knowledge Questionnaire)",
    image: mhkqImage,
    description: "MHKQ menilai tingkat pengetahuan seseorang tentang kesehatan mental, gangguan mental, dan cara penanganannya.",
    purpose: "Mengidentifikasi gap pengetahuan tentang kesehatan mental untuk merancang program edukasi yang tepat.",
    whatItMeasures: [
      "Pemahaman tentang gangguan mental",
      "Pengetahuan tentang faktor penyebab",
      "Kesadaran tentang pilihan pengobatan dan dukungan"
    ],
    howToUse: "Jawab setiap pernyataan dengan Benar atau Salah berdasarkan pengetahuan Anda saat ini.",
    interpretation: "Hasil menunjukkan tingkat literasi kesehatan mental Anda - area mana yang sudah dipahami dan mana yang perlu dipelajari lebih lanjut.",
    importance: "Pengetahuan yang baik tentang kesehatan mental membantu mengurangi stigma dan mendorong pencarian bantuan ketika diperlukan."
  },
  MSPSS: {
    title: "MSPSS (Multidimensional Scale of Perceived Social Support)",
    image: mspssImage,
    description: "MSPSS mengukur persepsi seseorang tentang dukungan sosial yang diterima dari tiga sumber utama: keluarga, teman, dan orang penting lainnya.",
    purpose: "Memahami seberapa kuat sistem dukungan sosial yang dirasakan, yang merupakan faktor protektif penting untuk kesehatan mental.",
    whatItMeasures: [
      "Dukungan dari keluarga",
      "Dukungan dari teman sebaya",
      "Dukungan dari orang penting (mentor, ustadz, dll)"
    ],
    howToUse: "Berikan penilaian dengan skala 1-7 (Sangat Tidak Setuju hingga Sangat Setuju) untuk setiap pernyataan tentang dukungan yang Anda rasakan.",
    interpretation: "Skor tinggi menunjukkan persepsi dukungan sosial yang kuat, yang berkontribusi positif terhadap kesejahteraan mental.",
    importance: "Dukungan sosial yang baik membantu mengatasi stres, meningkatkan resiliensi, dan mencegah masalah kesehatan mental."
  },
  PDD: {
    title: "PDD (Perceived Devaluation-Discrimination Scale)",
    image: pddImage,
    description: "PDD mengukur persepsi seseorang tentang stigma dan diskriminasi yang mungkin dialami oleh orang dengan masalah kesehatan mental di masyarakat.",
    purpose: "Memahami seberapa besar stigma yang dirasakan terhadap masalah kesehatan mental, yang dapat mempengaruhi kemauan untuk mencari bantuan.",
    whatItMeasures: [
      "Persepsi tentang sikap masyarakat terhadap gangguan mental",
      "Ekspektasi diskriminasi di tempat kerja/sekolah",
      "Penerimaan sosial terhadap orang dengan riwayat gangguan mental"
    ],
    howToUse: "Berikan penilaian dengan skala 1-5 berdasarkan persepsi Anda tentang bagaimana masyarakat memperlakukan orang dengan masalah kesehatan mental.",
    interpretation: "Skor tinggi menunjukkan persepsi stigma yang tinggi, yang dapat menjadi penghalang untuk mencari bantuan professional.",
    importance: "Memahami stigma membantu dalam upaya edukasi dan advokasi untuk menciptakan lingkungan yang lebih mendukung kesehatan mental."
  }
};

type Props = {
  assessmentKey: AssessmentKey;
  trigger?: React.ReactNode;
};

const AssessmentInfo = ({ assessmentKey, trigger }: Props) => {
  const info = assessmentInfo[assessmentKey];
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="gap-2">
            <InfoIcon className="h-4 w-4" />
            Pelajari Lebih Lanjut
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] overflow-auto" aria-describedby="assessment-description">
        <DialogHeader>
          <DialogTitle className="text-lg">{info.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="w-full h-48 rounded-lg overflow-hidden border">
            <img 
              src={info.image} 
              alt={info.title}
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="space-y-4">
            <section id="assessment-description">
              <h3 className="font-semibold text-base mb-2">Apa itu {info.title}?</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.description}</p>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Tujuan Assessment</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.purpose}</p>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Yang Diukur</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                {info.whatItMeasures.map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-primary mt-1">•</span>
                    <span className="leading-relaxed">{item}</span>
                  </li>
                ))}
              </ul>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Cara Penggunaan</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.howToUse}</p>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Interpretasi Hasil</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.interpretation}</p>
            </section>
            
            <section>
              <h3 className="font-semibold text-base mb-2">Mengapa Penting?</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">{info.importance}</p>
            </section>
            
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Catatan:</strong> Assessment ini adalah alat skrining dan bukan diagnosis. 
                Jika Anda memiliki kekhawatiran tentang kesehatan mental, konsultasikan dengan profesional kesehatan mental yang qualified.
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssessmentInfo;