import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { InfoIcon } from "lucide-react";
import FeatureInfo from "./FeatureInfo";

// Import improved anime-style feature images with futuristic pesantren tech
import assessmentImage from "@assets/generated_images/Anime_digital_platform_separate_8fac5857.png";
import educationImage from "@assets/generated_images/Anime_educational_resources_tech_0a3c4da5.png";
import gamesImage from "@assets/generated_images/Anime_interactive_games_futuristic_f1f714b4.png";
import analyticsImage from "@assets/generated_images/Anime_analytics_futuristic_tech_4639e93e.png";

const Features = () => {
  const items = [
    {
      title: "Assessment Terstruktur",
      desc: "Kuesioner valid: MHKQ, PDD, GSE, MSCS, SRQ-20, DASS-42.",
      image: assessmentImage,
      key: "assessment" as const,
    },
    {
      title: "Rekomendasi Terapeutik",
      desc: "Perawatan diri dan modifikasi perilaku terarah.",
      image: educationImage,
      key: "therapeutic" as const,
    },
    {
      title: "Edukasi Interaktif",
      desc: "E-modul, video, game anti-bullying, dan resource lainnya.",
      image: gamesImage,
      key: "education" as const,
    },
    {
      title: "Analytics & Riwayat",
      desc: "Lihat progres dari waktu ke waktu (coming soon).",
      image: analyticsImage,
      key: "analytics" as const,
    },
  ];

  return (
    <section id="features" className="container mx-auto px-6 py-12 md:py-16">
      <header className="mb-6">
        <h2 className="text-2xl md:text-3xl font-semibold">Fitur Utama</h2>
        <p className="text-muted-foreground mt-2 max-w-prose">
          Dirancang mobile-first dengan UI/UX modern, responsif, dan intuitif.
        </p>
      </header>
      <Separator />
      <div className="mt-8 grid gap-6 sm:grid-cols-2 lg:grid-cols-2">
        {items.map((f) => (
          <Card key={f.title} className="transition-transform hover:-translate-y-1 flex flex-col overflow-hidden">
            {/* Feature Image */}
            <div className="w-full h-48 overflow-hidden">
              <img 
                src={f.image} 
                alt={f.title}
                className="w-full h-full object-cover"
              />
            </div>
            
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{f.title}</CardTitle>
            </CardHeader>
            
            <CardContent className="text-sm text-muted-foreground flex flex-col gap-3 flex-grow">
              <p className="leading-relaxed">{f.desc}</p>
              
              <div className="mt-auto">
                <FeatureInfo 
                  featureKey={f.key}
                  trigger={
                    <Button variant="outline" size="sm" className="gap-2 w-full">
                      <InfoIcon className="h-4 w-4" />
                      Pelajari Lebih Lanjut
                    </Button>
                  }
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default Features;
