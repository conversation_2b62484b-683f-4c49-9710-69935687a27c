import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/hooks/use-toast";
import { ASSESSMENTS, AssessmentKey, scoreAssessment, getMaxTotals, analyzeAssessment } from "./assessments-data";
import { apiRequest } from "@/lib/queryClient";

type Props = { keyId: AssessmentKey; onExit?: () => void };

type FormVals = Record<string, number | boolean>;

const AssessmentRunner = ({ keyId, onExit }: Props) => {
  const cfg = ASSESSMENTS[keyId];
  const { register, watch, setValue, handleSubmit } = useForm<FormVals>({ mode: "onChange" });
  const [submitted, setSubmitted] = useState<{ total: number; domains: Record<string, number>; percent: number; level: string; summary: string } | null>(null);

  const answers = watch();
  const totalItems = cfg.questions.length;
  const answeredCount = useMemo(
    () => Object.keys(answers).filter((k) => answers[k] !== undefined).length,
    [answers]
  );
  const progress = Math.round((answeredCount / totalItems) * 100);

  const onSubmit = async (vals: FormVals) => {
    const result = scoreAssessment(keyId, vals);
    const max = getMaxTotals(keyId);
    const analysis = analyzeAssessment(keyId, result, max);
    setSubmitted({ ...result, percent: analysis.percent, level: analysis.level, summary: analysis.summary });
    const latestPayload = {
      key: keyId,
      total: result.total,
      domains: result.domains,
      ...max,
      percent: analysis.percent,
      level: analysis.level,
      summary: analysis.summary,
      completedAt: new Date().toISOString(),
    };
    localStorage.setItem(`assessment_latest_${keyId}`, JSON.stringify(latestPayload));
    toast({
      title: "Jawaban tersimpan",
      description: "Hasil akhir disimpan di perangkat ini.",
    });

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        toast({ title: "Belum masuk", description: "Masuk untuk menyinkronkan ke database.", });
        return;
      }

      await apiRequest('/api/assessments', {
        method: 'POST',
        body: JSON.stringify({
          jenisAssessment: keyId,
          hasilJawaban: vals,
          skorTotal: result.total,
          interpretasi: `${analysis.level} — ${analysis.summary}`,
          tanggalAssessment: new Date().toISOString(),
        }),
      });

      toast({ title: "Tersimpan ke database", description: "Hasil berhasil disinkronkan ke database." });
    } catch (e: any) {
      toast({ variant: "destructive", title: "Kesalahan", description: e?.message ?? "Terjadi kesalahan saat sinkron." });
    }
  };

  return (
    <div className="container mx-auto px-4 py-4 max-w-screen-sm">
      <button onClick={onExit} className="text-sm underline mb-2">Kembali</button>
      <h1 className="text-xl font-semibold">{cfg.title}</h1>
      <p className="text-muted-foreground text-sm mt-1">{cfg.description}</p>
      {cfg.scaleNote && <p className="text-xs mt-1">{cfg.scaleNote}</p>}

      <div className="mt-4">
        <Progress value={progress} />
        <p className="text-xs text-muted-foreground mt-1">{answeredCount}/{totalItems} terjawab</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-3 mt-4">
        {cfg.questions.map((q, idx) => (
          <Card key={q.id}>
            <CardHeader className="py-3">
              <CardTitle className="text-sm font-medium">
                {idx + 1}. {q.text}
                {q.domain ? <span className="text-xs text-muted-foreground"> • {q.domain}</span> : null}
              </CardTitle>
            </CardHeader>
            <CardContent className="py-3">
              {q.type === "likert" && q.options ? (
                <RadioGroup
                  onValueChange={(val) => setValue(q.id, Number(val))}
                  className="grid grid-cols-2 gap-2 sm:grid-cols-4"
                >
                  {q.options.map((opt) => (
                    <div key={opt.value} className="flex items-center gap-2 rounded-md border p-2">
                      <RadioGroupItem value={String(opt.value)} id={`${q.id}-${opt.value}`} />
                      <Label htmlFor={`${q.id}-${opt.value}`} className="text-xs">{opt.label}</Label>
                    </div>
                  ))}
                </RadioGroup>
              ) : (
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center gap-2 rounded-md border p-2">
                    <input type="radio" id={`${q.id}-true`} {...register(q.id)} onChange={() => setValue(q.id, true)} />
                    <Label htmlFor={`${q.id}-true`} className="text-xs">Benar</Label>
                  </div>
                  <div className="flex items-center gap-2 rounded-md border p-2">
                    <input type="radio" id={`${q.id}-false`} {...register(q.id)} onChange={() => setValue(q.id, false)} />
                    <Label htmlFor={`${q.id}-false`} className="text-xs">Salah</Label>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {!submitted ? (
          <Button type="submit" className="w-full" disabled={answeredCount < totalItems}>Kirim</Button>
        ) : (
          <div className="space-y-3">
            <Card>
              <CardHeader className="py-3"><CardTitle className="text-base">Hasil Ringkas</CardTitle></CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="text-3xl font-bold">{Math.round(submitted.percent)}%</div>
                  <span className="px-2 py-0.5 rounded-full bg-primary/10 text-primary text-xs">{submitted.level}</span>
                </div>
                <p className="text-sm">{submitted.summary}</p>
                <p className="text-xs text-muted-foreground">
                  Catatan: Versi demo—interpretasi indikatif, bukan diagnosis.
                </p>
              </CardContent>
            </Card>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" onClick={() => { setSubmitted(null); }}>Ubah Jawaban</Button>
              <Button onClick={onExit}>Selesai</Button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default AssessmentRunner;
