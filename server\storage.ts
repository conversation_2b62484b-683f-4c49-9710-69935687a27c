import { eq } from "drizzle-orm";
import { db } from "./db";
import crypto from "crypto";
import {
  users,
  profiles,
  assessmentSessions,
  assessmentResults,
  assessmentConfigs,
  type User,
  type Profile,
  type AssessmentSession,
  type AssessmentResult,
  type AssessmentConfig,
  type InsertUser,
  type InsertProfile,
  type InsertAssessmentSession,
  type InsertAssessmentResult
} from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Profile management
  getProfile(userId: string): Promise<Profile | undefined>;
  createProfile(profile: InsertProfile): Promise<Profile>;
  updateProfile(userId: string, profile: Partial<InsertProfile>): Promise<Profile | undefined>;

  // Assessment management
  createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession>;
  createAssessmentResult(result: InsertAssessmentResult): Promise<AssessmentResult>;
  getAssessmentResultsByUser(userId: string): Promise<AssessmentResult[]>;
  getAssessmentResult(id: string): Promise<AssessmentResult | undefined>;
  getAssessmentConfigs(): Promise<AssessmentConfig[]>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    // Generate UUID for the user
    const userId = crypto.randomUUID();
    const userWithId = { ...user, id: userId };

    await db.insert(users).values(userWithId);

    // Fetch the created user
    const result = await db.select().from(users).where(eq(users.id, userId)).limit(1);
    return result[0];
  }

  async getProfile(userId: string): Promise<Profile | undefined> {
    const result = await db.select().from(profiles).where(eq(profiles.userId, userId)).limit(1);
    return result[0];
  }

  async createProfile(profile: InsertProfile): Promise<Profile> {
    // Generate UUID for the profile
    const profileId = crypto.randomUUID();
    const profileWithId = { ...profile, id: profileId };

    await db.insert(profiles).values(profileWithId);

    // Fetch the created profile
    const result = await db.select().from(profiles).where(eq(profiles.id, profileId)).limit(1);
    return result[0];
  }

  async updateProfile(userId: string, profile: Partial<InsertProfile>): Promise<Profile | undefined> {
    await db.update(profiles)
      .set({ ...profile, updatedAt: new Date() })
      .where(eq(profiles.userId, userId));

    // Fetch the updated profile
    const result = await db.select().from(profiles).where(eq(profiles.userId, userId)).limit(1);
    return result[0];
  }

  async createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession> {
    // Generate UUID for the session
    const sessionId = crypto.randomUUID();
    const sessionWithId = { ...session, id: sessionId };

    await db.insert(assessmentSessions).values(sessionWithId);

    // Fetch the created session
    const result = await db.select().from(assessmentSessions).where(eq(assessmentSessions.id, sessionId)).limit(1);
    return result[0];
  }

  async createAssessmentResult(result: InsertAssessmentResult): Promise<AssessmentResult> {
    // Generate UUID for the result
    const resultId = crypto.randomUUID();
    const resultWithId = { ...result, id: resultId };

    await db.insert(assessmentResults).values(resultWithId);

    // Fetch the created result
    const resultData = await db.select().from(assessmentResults).where(eq(assessmentResults.id, resultId)).limit(1);
    return resultData[0];
  }

  async getAssessmentResultsByUser(userId: string): Promise<AssessmentResult[]> {
    return await db.select().from(assessmentResults).where(eq(assessmentResults.userId, userId));
  }

  async getAssessmentResult(id: string): Promise<AssessmentResult | undefined> {
    const result = await db.select().from(assessmentResults).where(eq(assessmentResults.id, id)).limit(1);
    return result[0];
  }

  async getAssessmentConfigs(): Promise<AssessmentConfig[]> {
    return await db.select().from(assessmentConfigs).where(eq(assessmentConfigs.isActive, true));
  }
}

// Database storage implementation is available above
export class MemStorage implements IStorage {
  private users: Map<string, User>;
  private profiles: Map<string, Profile>;
  private assessmentSessions: Map<string, AssessmentSession>;
  private assessmentResults: Map<string, AssessmentResult>;
  private assessmentConfigs: Map<string, AssessmentConfig>;

  constructor() {
    this.users = new Map();
    this.profiles = new Map();
    this.assessmentSessions = new Map();
    this.assessmentResults = new Map();
    this.assessmentConfigs = new Map();

    // Add some sample assessment configs
    this.initSampleConfigs();
  }

  private initSampleConfigs() {
    const configs: AssessmentConfig[] = [
      {
        id: crypto.randomUUID(),
        assessmentCode: 'DASS42',
        assessmentName: 'Depression Anxiety Stress Scales-42',
        description: 'Mengukur tingkat depresi, kecemasan, dan stres',
        version: '1.0',
        totalItems: 42,
        estimatedTimeMinutes: 15,
        isActive: true,
        requiresSupervision: false,
        ageMin: 12,
        ageMax: 30,
        culturalContext: 'pesantren',
        scoringMethod: 'likert',
        clinicalCutoffs: '{"depression":{"normal":9,"mild":13,"moderate":20,"severe":27}}',
        psychometricProperties: '{"reliability":0.91,"validity":"high"}',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    configs.forEach(config => this.assessmentConfigs.set(config.id, config));
  }

  async getUser(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = crypto.randomUUID();
    const user: User = { 
      ...insertUser, 
      id, 
      createdAt: new Date(), 
      updatedAt: new Date() 
    };
    this.users.set(id, user);
    return user;
  }

  async getProfile(userId: string): Promise<Profile | undefined> {
    return Array.from(this.profiles.values()).find(profile => profile.userId === userId);
  }

  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    const id = crypto.randomUUID();
    const profile: Profile = { 
      ...insertProfile,
      nomorInduk: insertProfile.nomorInduk ?? null,
      jenisKelamin: insertProfile.jenisKelamin ?? null,
      tanggalLahir: insertProfile.tanggalLahir ?? null,
      kelas: insertProfile.kelas ?? null,
      pondokPesantren: insertProfile.pondokPesantren ?? null,
      id, 
      createdAt: new Date(), 
      updatedAt: new Date() 
    };
    this.profiles.set(id, profile);
    return profile;
  }

  async updateProfile(userId: string, updateData: Partial<InsertProfile>): Promise<Profile | undefined> {
    const existing = await this.getProfile(userId);
    if (!existing) return undefined;
    
    const updated: Profile = { 
      ...existing, 
      ...updateData, 
      updatedAt: new Date() 
    };
    this.profiles.set(existing.id, updated);
    return updated;
  }

  async createAssessmentSession(insertSession: InsertAssessmentSession): Promise<AssessmentSession> {
    const id = crypto.randomUUID();
    const session: AssessmentSession = {
      ...insertSession,
      id,
      sessionStatus: insertSession.sessionStatus ?? 'started',
      startedAt: insertSession.startedAt ?? new Date(),
      completedAt: insertSession.completedAt ?? null,
      durationSeconds: insertSession.durationSeconds ?? null,
      ipAddress: insertSession.ipAddress ?? null,
      userAgent: insertSession.userAgent ?? null,
      deviceType: insertSession.deviceType ?? null,
      isSupervised: insertSession.isSupervised ?? false,
      supervisorId: insertSession.supervisorId ?? null,
      notes: insertSession.notes ?? null
    };
    this.assessmentSessions.set(id, session);
    return session;
  }

  async createAssessmentResult(insertResult: InsertAssessmentResult): Promise<AssessmentResult> {
    const id = crypto.randomUUID();
    const result: AssessmentResult = {
      ...insertResult,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.assessmentResults.set(id, result);
    return result;
  }

  async getAssessmentResultsByUser(userId: string): Promise<AssessmentResult[]> {
    return Array.from(this.assessmentResults.values()).filter(result => result.userId === userId);
  }

  async getAssessmentResult(id: string): Promise<AssessmentResult | undefined> {
    return this.assessmentResults.get(id);
  }

  async getAssessmentConfigs(): Promise<AssessmentConfig[]> {
    return Array.from(this.assessmentConfigs.values()).filter(config => config.isActive);
  }
}

// Import offline storage
import { OfflineStorage } from './offline-storage';

// Switch between storage implementations
export const storage = process.env.NODE_ENV === 'production' && process.env.DATABASE_URL
  ? new DatabaseStorage()
  : process.env.ENABLE_OFFLINE
    ? new OfflineStorage()
    : new MemStorage();
