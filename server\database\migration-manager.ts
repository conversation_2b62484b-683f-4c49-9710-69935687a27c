/**
 * 🔄 Database Migration Manager
 * 
 * Handles database migrations across multiple providers
 */

import { ConnectionManager } from './connection-manager';
import { SchemaAnalyzer, TableInfo } from './schema-analyzer';
import fs from 'fs';
import path from 'path';

export interface MigrationInfo {
  id: string;
  name: string;
  version: string;
  provider: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  appliedAt?: Date;
  error?: string;
}

export interface DataMigrationResult {
  success: boolean;
  tablesProcessed: number;
  recordsMigrated: number;
  errors: string[];
  duration: number;
}

export class MigrationManager {
  private connectionManager: ConnectionManager;
  private schemaAnalyzer: SchemaAnalyzer;

  constructor() {
    this.connectionManager = ConnectionManager.getInstance();
    this.schemaAnalyzer = new SchemaAnalyzer();
  }

  /**
   * Initialize migration tracking table
   */
  async initializeMigrationTracking(): Promise<void> {
    const connection = await this.connectionManager.getConnection();
    const provider = this.connectionManager.getCurrentProvider();

    const createTableSQL = this.getMigrationTableSQL(provider);
    
    try {
      if (provider === 'sqlite') {
        connection.db.exec(createTableSQL);
      } else {
        await connection.getPool().execute(createTableSQL);
      }
      console.log('✅ Migration tracking table initialized');
    } catch (error) {
      console.error('❌ Error initializing migration tracking:', error);
      throw error;
    }
  }

  /**
   * Get migration table SQL for different providers
   */
  private getMigrationTableSQL(provider: string): string {
    switch (provider) {
      case 'mysql':
      case 'aiven-mysql':
        return `
          CREATE TABLE IF NOT EXISTS schema_migrations (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            migration_id VARCHAR(100) NOT NULL UNIQUE,
            migration_name VARCHAR(255) NOT NULL,
            version VARCHAR(20) NOT NULL,
            provider VARCHAR(50) NOT NULL,
            status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
            applied_at TIMESTAMP NULL,
            error_message TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_migrations_status (status),
            INDEX idx_migrations_provider (provider)
          )
        `;
      
      case 'neon-postgresql':
        return `
          CREATE TABLE IF NOT EXISTS schema_migrations (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            migration_id VARCHAR(100) NOT NULL UNIQUE,
            migration_name VARCHAR(255) NOT NULL,
            version VARCHAR(20) NOT NULL,
            provider VARCHAR(50) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
            applied_at TIMESTAMP NULL,
            error_message TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          CREATE INDEX IF NOT EXISTS idx_migrations_status ON schema_migrations(status);
          CREATE INDEX IF NOT EXISTS idx_migrations_provider ON schema_migrations(provider);
        `;
      
      case 'sqlite':
        return `
          CREATE TABLE IF NOT EXISTS schema_migrations (
            id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
            migration_id TEXT NOT NULL UNIQUE,
            migration_name TEXT NOT NULL,
            version TEXT NOT NULL,
            provider TEXT NOT NULL,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
            applied_at DATETIME NULL,
            error_message TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          );
          CREATE INDEX IF NOT EXISTS idx_migrations_status ON schema_migrations(status);
          CREATE INDEX IF NOT EXISTS idx_migrations_provider ON schema_migrations(provider);
        `;
      
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Apply schema migration to current database
   */
  async applySchema(): Promise<void> {
    const provider = this.connectionManager.getCurrentProvider();
    const schemaPath = this.getSchemaPath(provider);
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Schema file not found: ${schemaPath}`);
    }

    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    const connection = await this.connectionManager.getConnection();

    try {
      console.log(`🔄 Applying schema for ${provider}...`);
      
      if (provider === 'sqlite') {
        // SQLite: Execute schema in parts
        const statements = this.splitSQLStatements(schemaSQL);
        for (const statement of statements) {
          if (statement.trim()) {
            connection.db.exec(statement);
          }
        }
      } else {
        // MySQL/PostgreSQL: Execute schema
        const statements = this.splitSQLStatements(schemaSQL);
        for (const statement of statements) {
          if (statement.trim() && !statement.trim().startsWith('--')) {
            await connection.getPool().execute(statement);
          }
        }
      }

      // Record migration
      await this.recordMigration({
        id: `schema_${provider}_${Date.now()}`,
        name: `Initial schema for ${provider}`,
        version: '1.0.0',
        provider,
        status: 'completed',
        appliedAt: new Date()
      });

      console.log(`✅ Schema applied successfully for ${provider}`);
    } catch (error) {
      console.error(`❌ Error applying schema for ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Get schema file path for provider
   */
  private getSchemaPath(provider: string): string {
    const baseDir = path.join(process.cwd(), 'database');
    
    switch (provider) {
      case 'mysql':
      case 'aiven-mysql':
        return path.join(baseDir, 'mysql-schema.sql');
      case 'neon-postgresql':
        return path.join(baseDir, 'postgresql-schema.sql');
      case 'sqlite':
        return path.join(baseDir, 'sqlite-schema.sql');
      default:
        return path.join(baseDir, 'schema.sql');
    }
  }

  /**
   * Split SQL into individual statements
   */
  private splitSQLStatements(sql: string): string[] {
    // Remove comments and split by semicolon
    const cleaned = sql
      .replace(/--.*$/gm, '') // Remove line comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .replace(/\n\s*\n/g, '\n'); // Remove empty lines
    
    return cleaned
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
  }

  /**
   * Migrate data between providers
   */
  async migrateData(sourceProvider: string, targetProvider: string): Promise<DataMigrationResult> {
    const startTime = Date.now();
    const result: DataMigrationResult = {
      success: false,
      tablesProcessed: 0,
      recordsMigrated: 0,
      errors: [],
      duration: 0
    };

    try {
      console.log(`🔄 Starting data migration from ${sourceProvider} to ${targetProvider}...`);

      // Get source connection
      await this.connectionManager.switchProvider(sourceProvider);
      const sourceConnection = await this.connectionManager.getConnection();
      const sourceSchema = await this.schemaAnalyzer.getCurrentSchema();

      // Get target connection
      await this.connectionManager.switchProvider(targetProvider);
      const targetConnection = await this.connectionManager.getConnection();

      // Ensure target schema exists
      await this.applySchema();

      // Migrate data table by table
      for (const table of sourceSchema) {
        try {
          console.log(`📋 Migrating table: ${table.name}`);
          
          const recordCount = await this.migrateTableData(
            sourceConnection,
            targetConnection,
            table,
            sourceProvider,
            targetProvider
          );
          
          result.recordsMigrated += recordCount;
          result.tablesProcessed++;
          
          console.log(`✅ Migrated ${recordCount} records from ${table.name}`);
        } catch (error) {
          const errorMsg = `Error migrating table ${table.name}: ${error}`;
          console.error(`❌ ${errorMsg}`);
          result.errors.push(errorMsg);
        }
      }

      result.success = result.errors.length === 0;
      result.duration = Date.now() - startTime;

      console.log(`🎉 Data migration completed in ${result.duration}ms`);
      console.log(`📊 Tables: ${result.tablesProcessed}, Records: ${result.recordsMigrated}`);
      
      if (result.errors.length > 0) {
        console.log(`⚠️  Errors: ${result.errors.length}`);
      }

      return result;
    } catch (error) {
      result.success = false;
      result.duration = Date.now() - startTime;
      result.errors.push(`Migration failed: ${error}`);
      console.error('❌ Data migration failed:', error);
      return result;
    }
  }

  /**
   * Migrate data for a single table
   */
  private async migrateTableData(
    sourceConnection: any,
    targetConnection: any,
    table: TableInfo,
    sourceProvider: string,
    targetProvider: string
  ): Promise<number> {
    // Get data from source
    const sourceData = await this.getTableData(sourceConnection, table.name, sourceProvider);
    
    if (sourceData.length === 0) {
      return 0;
    }

    // Clear target table
    await this.clearTable(targetConnection, table.name, targetProvider);

    // Insert data into target
    await this.insertTableData(targetConnection, table.name, sourceData, targetProvider);

    return sourceData.length;
  }

  /**
   * Get all data from a table
   */
  private async getTableData(connection: any, tableName: string, provider: string): Promise<any[]> {
    if (provider === 'sqlite') {
      return connection.db.prepare(`SELECT * FROM ${tableName}`).all();
    } else {
      const [rows] = await connection.getPool().execute(`SELECT * FROM ${tableName}`);
      return rows as any[];
    }
  }

  /**
   * Clear table data
   */
  private async clearTable(connection: any, tableName: string, provider: string): Promise<void> {
    if (provider === 'sqlite') {
      connection.db.prepare(`DELETE FROM ${tableName}`).run();
    } else {
      await connection.getPool().execute(`DELETE FROM ${tableName}`);
    }
  }

  /**
   * Insert data into table
   */
  private async insertTableData(
    connection: any,
    tableName: string,
    data: any[],
    provider: string
  ): Promise<void> {
    if (data.length === 0) return;

    const columns = Object.keys(data[0]);
    const placeholders = columns.map(() => '?').join(', ');
    const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

    if (provider === 'sqlite') {
      const stmt = connection.db.prepare(sql);
      for (const row of data) {
        const values = columns.map(col => row[col]);
        stmt.run(values);
      }
    } else {
      for (const row of data) {
        const values = columns.map(col => row[col]);
        await connection.getPool().execute(sql, values);
      }
    }
  }

  /**
   * Record migration in tracking table
   */
  private async recordMigration(migration: MigrationInfo): Promise<void> {
    const connection = await this.connectionManager.getConnection();
    const provider = this.connectionManager.getCurrentProvider();

    const sql = `
      INSERT INTO schema_migrations 
      (migration_id, migration_name, version, provider, status, applied_at, error_message)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      migration.id,
      migration.name,
      migration.version,
      migration.provider,
      migration.status,
      migration.appliedAt,
      migration.error
    ];

    try {
      if (provider === 'sqlite') {
        connection.db.prepare(sql).run(values);
      } else {
        await connection.getPool().execute(sql, values);
      }
    } catch (error) {
      console.error('Error recording migration:', error);
    }
  }

  /**
   * Get migration history
   */
  async getMigrationHistory(): Promise<MigrationInfo[]> {
    const connection = await this.connectionManager.getConnection();
    const provider = this.connectionManager.getCurrentProvider();

    try {
      if (provider === 'sqlite') {
        const rows = connection.db.prepare(`
          SELECT * FROM schema_migrations 
          ORDER BY created_at DESC
        `).all();
        return rows;
      } else {
        const [rows] = await connection.getPool().execute(`
          SELECT * FROM schema_migrations 
          ORDER BY created_at DESC
        `);
        return rows as MigrationInfo[];
      }
    } catch (error) {
      console.error('Error getting migration history:', error);
      return [];
    }
  }
}
