#!/usr/bin/env node

/**
 * 🔄 Database Migration Testing Script
 * 
 * Tests database migration functionality across all providers
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';
const API_BASE = `${BASE_URL}/api`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔄 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(40));
  log(`📋 ${message}`, 'yellow');
  console.log('-'.repeat(40));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test migration status
async function testMigrationStatus() {
  logSubHeader('Migration Status Check');
  
  try {
    const response = await fetch(`${API_BASE}/migration/status`);
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Migration status endpoint working');
      
      const data = result.data;
      logInfo(`Current Provider: ${data.currentProvider}`);
      logInfo(`Total Tables: ${data.schemaStatus.totalTables}`);
      logInfo(`Expected Tables: ${data.schemaStatus.expectedTables}`);
      logInfo(`Schema Complete: ${data.schemaStatus.isComplete ? 'Yes' : 'No'}`);
      
      if (data.schemaStatus.missingTables.length > 0) {
        logWarning(`Missing Tables: ${data.schemaStatus.missingTables.join(', ')}`);
      }
      
      if (data.lastMigration) {
        logInfo(`Last Migration: ${data.lastMigration.migration_name} (${data.lastMigration.status})`);
      }
      
      return true;
    } else {
      logError(`Migration status failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Migration status error: ${error.message}`);
    return false;
  }
}

// Test schema report
async function testSchemaReport() {
  logSubHeader('Schema Report Generation');
  
  try {
    const response = await fetch(`${API_BASE}/migration/schema-report`);
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Schema report generated successfully');
      
      const tables = result.data.tables;
      logInfo(`Database contains ${tables.length} tables:`);
      
      tables.forEach(table => {
        logInfo(`  - ${table.name}: ${table.columnCount} columns, ${table.indexCount} indexes`);
      });
      
      return true;
    } else {
      logError(`Schema report failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Schema report error: ${error.message}`);
    return false;
  }
}

// Test schema application
async function testApplySchema() {
  logSubHeader('Schema Application Test');
  
  try {
    logInfo('Applying schema to current database...');
    
    const response = await fetch(`${API_BASE}/migration/apply-schema`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess(`Schema applied successfully for ${result.data.provider}`);
      logInfo(`Tables created: ${result.data.tablesCreated}`);
      logInfo(`Tables: ${result.data.tables.join(', ')}`);
      return true;
    } else {
      logError(`Schema application failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Schema application error: ${error.message}`);
    return false;
  }
}

// Test provider information
async function testProviders() {
  logSubHeader('Database Providers Check');
  
  try {
    const response = await fetch(`${API_BASE}/migration/providers`);
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Provider information retrieved');
      
      const data = result.data;
      logInfo(`Current Provider: ${data.currentProvider}`);
      logInfo(`Available Providers: ${data.availableCount}/${data.providers.length}`);
      
      data.providers.forEach(provider => {
        const status = provider.isActive ? '🎯 ACTIVE' : 
                      provider.isAvailable ? '✅ Available' : '❌ Unavailable';
        logInfo(`  - ${provider.displayName}: ${status}`);
        logInfo(`    ${provider.description}`);
      });
      
      return true;
    } else {
      logError(`Provider check failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Provider check error: ${error.message}`);
    return false;
  }
}

// Test backup creation
async function testBackup() {
  logSubHeader('Backup Creation Test');
  
  try {
    const backupData = {
      name: `Test Backup ${new Date().toISOString()}`,
      description: 'Automated test backup'
    };
    
    const response = await fetch(`${API_BASE}/migration/backup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(backupData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Backup created successfully');
      
      const backup = result.data;
      logInfo(`Backup ID: ${backup.id}`);
      logInfo(`Backup Name: ${backup.name}`);
      logInfo(`Provider: ${backup.provider}`);
      logInfo(`Tables: ${backup.tableCount}`);
      logInfo(`Total Columns: ${backup.totalColumns}`);
      
      return true;
    } else {
      logError(`Backup creation failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Backup creation error: ${error.message}`);
    return false;
  }
}

// Test migration history
async function testMigrationHistory() {
  logSubHeader('Migration History Check');
  
  try {
    const response = await fetch(`${API_BASE}/migration/history`);
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Migration history retrieved');
      
      const data = result.data;
      logInfo(`Total Migrations: ${data.totalCount}`);
      logInfo(`Completed: ${data.completedCount}`);
      logInfo(`Failed: ${data.failedCount}`);
      
      if (data.migrations.length > 0) {
        logInfo('Recent migrations:');
        data.migrations.slice(0, 5).forEach(migration => {
          const status = migration.status === 'completed' ? '✅' : 
                        migration.status === 'failed' ? '❌' : '⏳';
          logInfo(`  ${status} ${migration.migration_name} (${migration.provider})`);
        });
      }
      
      return true;
    } else {
      logError(`Migration history failed: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Migration history error: ${error.message}`);
    return false;
  }
}

// Test data migration (if multiple providers available)
async function testDataMigration() {
  logSubHeader('Data Migration Test');
  
  try {
    // First get available providers
    const providersResponse = await fetch(`${API_BASE}/migration/providers`);
    const providersResult = await providersResponse.json();
    
    if (!providersResult.success) {
      logWarning('Cannot test data migration - provider info unavailable');
      return false;
    }
    
    const availableProviders = providersResult.data.providers
      .filter(p => p.isAvailable && !p.isActive)
      .map(p => p.name);
    
    if (availableProviders.length === 0) {
      logWarning('Cannot test data migration - need at least 2 available providers');
      logInfo('This is normal if only one database is configured');
      return true; // Not a failure, just not testable
    }
    
    const currentProvider = providersResult.data.currentProvider;
    const targetProvider = availableProviders[0];
    
    logInfo(`Testing migration from ${currentProvider} to ${targetProvider}...`);
    
    const migrationData = {
      sourceProvider: currentProvider,
      targetProvider: targetProvider
    };
    
    const response = await fetch(`${API_BASE}/migration/migrate-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(migrationData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      logSuccess('Data migration completed successfully');
      
      const data = result.data;
      logInfo(`Tables Processed: ${data.tablesProcessed}`);
      logInfo(`Records Migrated: ${data.recordsMigrated}`);
      logInfo(`Duration: ${data.duration}ms`);
      
      if (data.errors.length > 0) {
        logWarning(`Errors encountered: ${data.errors.length}`);
        data.errors.forEach(error => logWarning(`  - ${error}`));
      }
      
      return true;
    } else {
      logError(`Data migration failed: ${result.error}`);
      if (result.data && result.data.errors) {
        result.data.errors.forEach(error => logError(`  - ${error}`));
      }
      return false;
    }
  } catch (error) {
    logError(`Data migration error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runMigrationTests() {
  logHeader('Database Migration Testing Suite');
  
  const tests = [
    { name: 'Migration Status', fn: testMigrationStatus },
    { name: 'Schema Report', fn: testSchemaReport },
    { name: 'Apply Schema', fn: testApplySchema },
    { name: 'Providers Check', fn: testProviders },
    { name: 'Backup Creation', fn: testBackup },
    { name: 'Migration History', fn: testMigrationHistory },
    { name: 'Data Migration', fn: testDataMigration }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      logError(`Test ${test.name} threw an error: ${error.message}`);
      results.push({ name: test.name, success: false, error: error.message });
    }
  }
  
  // Summary
  logHeader('Migration Test Results');
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    log(`${status} ${result.name}`, result.success ? 'green' : 'red');
    if (result.error) {
      logError(`    Error: ${result.error}`);
    }
  });
  
  logInfo(`\nResults: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    logSuccess('🎉 All migration tests passed! Migration system is fully functional.');
  } else {
    logWarning(`⚠️  ${total - passed} tests failed. Migration system needs attention.`);
  }
  
  return passed === total;
}

// Run the tests
runMigrationTests().catch(error => {
  logError(`Migration test suite failed: ${error.message}`);
  process.exit(1);
});
