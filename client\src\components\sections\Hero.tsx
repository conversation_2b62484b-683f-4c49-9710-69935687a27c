import { But<PERSON> } from "@/components/ui/button";
import heroImg from "@assets/generated_images/Anime_female_student_assessment_dfd43af5.png";
import { useRef } from "react";
import { ArrowR<PERSON>, PlayCircle, Brain } from "lucide-react";
import { <PERSON> } from "wouter";

const Hero = () => {
  const ref = useRef<HTMLDivElement>(null);

  const onMouseMove: React.MouseEventHandler<HTMLDivElement> = (e) => {
    const el = ref.current;
    if (!el) return;
    const rect = el.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    el.style.setProperty("--mouse-x", `${x}%`);
    el.style.setProperty("--mouse-y", `${y}%`);
  };

  return (
    <section id="home" className="pt-8 md:pt-16">
      <div
        ref={ref}
        onMouseMove={onMouseMove}
        className="interactive-surface rounded-2xl border shadow-elegant bg-card"
      >
        <div className="container mx-auto px-6 py-12 md:py-20 grid md:grid-cols-2 gap-10 items-center">
          <div>
            <span className="inline-flex items-center px-3 py-1 rounded-full bg-secondary text-secondary-foreground text-xs">
              TOKEN PEDIA · Kesehatan Jiwa Santri
            </span>
            <h1 className="mt-4 text-4xl md:text-5xl font-bold leading-tight">
              SantriMental — Screening, Terapi, dan Edukasi Kesehatan Jiwa
            </h1>
            <p className="mt-4 text-muted-foreground max-w-prose">
              Bantu santri mengenali kondisi psikologisnya melalui assessment
              terstruktur (MHKQ, PDD, GSE, MSCS, SRQ-20, DASS-42), rekomendasi
              terapeutik, dan materi edukasi interaktif.
            </p>
            <div className="mt-6 flex flex-col sm:flex-row gap-3">
              <Link href="/assessments">
                <Button
                  variant="default"
                  size="lg"
                  className="w-full sm:w-auto gap-2"
                  data-testid="button-start-assessment"
                >
                  <Brain className="w-5 h-5" />
                  Mulai Assessment
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
              <Button
                variant="outline"
                size="lg"
                className="w-full sm:w-auto gap-2"
                onClick={() => document.querySelector("#education")?.scrollIntoView({ behavior: "smooth" })}
                data-testid="button-explore-education"
              >
                <PlayCircle className="w-5 h-5" />
                Jelajahi Edukasi
              </Button>
            </div>
          </div>
          <div className="relative">
            <img
              src={heroImg}
              alt="Ilustrasi anime pesantren mental health - santri Muslim dengan teknologi kesehatan jiwa"
              loading="eager"
              className="w-full h-auto rounded-xl border"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
